#
# Generated file, do not edit.
#

list(APPEND FLUTTER_PLUGIN_LIST
  app_links
  audioplayers_windows
  awesome_notifications
  cloud_firestore
  connectivity_plus
  desktop_webview_auth
  desktop_window
  dynamic_color
  file_selector_windows
  firebase_auth
  firebase_core
  firebase_storage
  flutter_inappwebview_windows
  flutter_tts
  flutter_webrtc
  gal
  geolocator_windows
  livekit_client
  local_auth_windows
  maps_launcher
  pdfx
  permission_handler_windows
  printing
  record_windows
  share_plus
  sqlite3_flutter_libs
  url_launcher_windows
)

list(APPEND FLUTTER_FFI_PLUGIN_LIST
  ggml_library_flutter
  llama_library_flutter
  maid_llm
  onnxruntime
  outetts_flutter
  whisper_library_flutter
)

set(PLUGIN_BUNDLED_LIBRARIES)

foreach(plugin ${FLUTTER_PLUGIN_LIST})
  add_subdirectory(flutter/ephemeral/.plugin_symlinks/${plugin}/windows plugins/${plugin})
  target_link_libraries(${BINARY_NAME} PRIVATE ${plugin}_plugin)
  list(APPEND PLUGIN_BUNDLED_LIBRARIES $<TARGET_FILE:${plugin}_plugin>)
  list(APPEND PLUGIN_BUNDLED_LIBRARIES ${${plugin}_bundled_libraries})
endforeach(plugin)

foreach(ffi_plugin ${FLUTTER_FFI_PLUGIN_LIST})
  add_subdirectory(flutter/ephemeral/.plugin_symlinks/${ffi_plugin}/windows plugins/${ffi_plugin})
  list(APPEND PLUGIN_BUNDLED_LIBRARIES ${${ffi_plugin}_bundled_libraries})
endforeach(ffi_plugin)
