PODS:
  - abseil/algorithm (1.20240116.2):
    - abseil/algorithm/algorithm (= 1.20240116.2)
    - abseil/algorithm/container (= 1.20240116.2)
  - abseil/algorithm/algorithm (1.20240116.2):
    - abseil/base/config
    - abseil/xcprivacy
  - abseil/algorithm/container (1.20240116.2):
    - abseil/algorithm/algorithm
    - abseil/base/core_headers
    - abseil/base/nullability
    - abseil/meta/type_traits
    - abseil/xcprivacy
  - abseil/base (1.20240116.2):
    - abseil/base/atomic_hook (= 1.20240116.2)
    - abseil/base/base (= 1.20240116.2)
    - abseil/base/base_internal (= 1.20240116.2)
    - abseil/base/config (= 1.20240116.2)
    - abseil/base/core_headers (= 1.20240116.2)
    - abseil/base/cycleclock_internal (= 1.20240116.2)
    - abseil/base/dynamic_annotations (= 1.20240116.2)
    - abseil/base/endian (= 1.20240116.2)
    - abseil/base/errno_saver (= 1.20240116.2)
    - abseil/base/fast_type_id (= 1.20240116.2)
    - abseil/base/log_severity (= 1.20240116.2)
    - abseil/base/malloc_internal (= 1.20240116.2)
    - abseil/base/no_destructor (= 1.20240116.2)
    - abseil/base/nullability (= 1.20240116.2)
    - abseil/base/prefetch (= 1.20240116.2)
    - abseil/base/pretty_function (= 1.20240116.2)
    - abseil/base/raw_logging_internal (= 1.20240116.2)
    - abseil/base/spinlock_wait (= 1.20240116.2)
    - abseil/base/strerror (= 1.20240116.2)
    - abseil/base/throw_delegate (= 1.20240116.2)
  - abseil/base/atomic_hook (1.20240116.2):
    - abseil/base/config
    - abseil/base/core_headers
    - abseil/xcprivacy
  - abseil/base/base (1.20240116.2):
    - abseil/base/atomic_hook
    - abseil/base/base_internal
    - abseil/base/config
    - abseil/base/core_headers
    - abseil/base/cycleclock_internal
    - abseil/base/dynamic_annotations
    - abseil/base/log_severity
    - abseil/base/nullability
    - abseil/base/raw_logging_internal
    - abseil/base/spinlock_wait
    - abseil/meta/type_traits
    - abseil/xcprivacy
  - abseil/base/base_internal (1.20240116.2):
    - abseil/base/config
    - abseil/meta/type_traits
    - abseil/xcprivacy
  - abseil/base/config (1.20240116.2):
    - abseil/xcprivacy
  - abseil/base/core_headers (1.20240116.2):
    - abseil/base/config
    - abseil/xcprivacy
  - abseil/base/cycleclock_internal (1.20240116.2):
    - abseil/base/base_internal
    - abseil/base/config
    - abseil/xcprivacy
  - abseil/base/dynamic_annotations (1.20240116.2):
    - abseil/base/config
    - abseil/base/core_headers
    - abseil/xcprivacy
  - abseil/base/endian (1.20240116.2):
    - abseil/base/base
    - abseil/base/config
    - abseil/base/core_headers
    - abseil/base/nullability
    - abseil/xcprivacy
  - abseil/base/errno_saver (1.20240116.2):
    - abseil/base/config
    - abseil/xcprivacy
  - abseil/base/fast_type_id (1.20240116.2):
    - abseil/base/config
    - abseil/xcprivacy
  - abseil/base/log_severity (1.20240116.2):
    - abseil/base/config
    - abseil/base/core_headers
    - abseil/xcprivacy
  - abseil/base/malloc_internal (1.20240116.2):
    - abseil/base/base
    - abseil/base/base_internal
    - abseil/base/config
    - abseil/base/core_headers
    - abseil/base/dynamic_annotations
    - abseil/base/raw_logging_internal
    - abseil/xcprivacy
  - abseil/base/no_destructor (1.20240116.2):
    - abseil/base/config
    - abseil/xcprivacy
  - abseil/base/nullability (1.20240116.2):
    - abseil/base/core_headers
    - abseil/meta/type_traits
    - abseil/xcprivacy
  - abseil/base/prefetch (1.20240116.2):
    - abseil/base/config
    - abseil/base/core_headers
    - abseil/xcprivacy
  - abseil/base/pretty_function (1.20240116.2):
    - abseil/xcprivacy
  - abseil/base/raw_logging_internal (1.20240116.2):
    - abseil/base/atomic_hook
    - abseil/base/config
    - abseil/base/core_headers
    - abseil/base/errno_saver
    - abseil/base/log_severity
    - abseil/xcprivacy
  - abseil/base/spinlock_wait (1.20240116.2):
    - abseil/base/base_internal
    - abseil/base/core_headers
    - abseil/base/errno_saver
    - abseil/xcprivacy
  - abseil/base/strerror (1.20240116.2):
    - abseil/base/config
    - abseil/base/core_headers
    - abseil/base/errno_saver
    - abseil/xcprivacy
  - abseil/base/throw_delegate (1.20240116.2):
    - abseil/base/config
    - abseil/base/raw_logging_internal
    - abseil/xcprivacy
  - abseil/cleanup/cleanup (1.20240116.2):
    - abseil/base/config
    - abseil/base/core_headers
    - abseil/cleanup/cleanup_internal
    - abseil/xcprivacy
  - abseil/cleanup/cleanup_internal (1.20240116.2):
    - abseil/base/base_internal
    - abseil/base/core_headers
    - abseil/utility/utility
    - abseil/xcprivacy
  - abseil/container/common (1.20240116.2):
    - abseil/meta/type_traits
    - abseil/types/optional
    - abseil/xcprivacy
  - abseil/container/common_policy_traits (1.20240116.2):
    - abseil/meta/type_traits
    - abseil/xcprivacy
  - abseil/container/compressed_tuple (1.20240116.2):
    - abseil/utility/utility
    - abseil/xcprivacy
  - abseil/container/container_memory (1.20240116.2):
    - abseil/base/config
    - abseil/memory/memory
    - abseil/meta/type_traits
    - abseil/utility/utility
    - abseil/xcprivacy
  - abseil/container/fixed_array (1.20240116.2):
    - abseil/algorithm/algorithm
    - abseil/base/config
    - abseil/base/core_headers
    - abseil/base/dynamic_annotations
    - abseil/base/throw_delegate
    - abseil/container/compressed_tuple
    - abseil/memory/memory
    - abseil/xcprivacy
  - abseil/container/flat_hash_map (1.20240116.2):
    - abseil/algorithm/container
    - abseil/base/core_headers
    - abseil/container/container_memory
    - abseil/container/hash_function_defaults
    - abseil/container/raw_hash_map
    - abseil/memory/memory
    - abseil/xcprivacy
  - abseil/container/flat_hash_set (1.20240116.2):
    - abseil/algorithm/container
    - abseil/base/core_headers
    - abseil/container/container_memory
    - abseil/container/hash_function_defaults
    - abseil/container/raw_hash_set
    - abseil/memory/memory
    - abseil/xcprivacy
  - abseil/container/hash_function_defaults (1.20240116.2):
    - abseil/base/config
    - abseil/hash/hash
    - abseil/strings/cord
    - abseil/strings/strings
    - abseil/xcprivacy
  - abseil/container/hash_policy_traits (1.20240116.2):
    - abseil/container/common_policy_traits
    - abseil/meta/type_traits
    - abseil/xcprivacy
  - abseil/container/hashtable_debug_hooks (1.20240116.2):
    - abseil/base/config
    - abseil/xcprivacy
  - abseil/container/hashtablez_sampler (1.20240116.2):
    - abseil/base/base
    - abseil/base/config
    - abseil/base/core_headers
    - abseil/base/raw_logging_internal
    - abseil/debugging/stacktrace
    - abseil/memory/memory
    - abseil/profiling/exponential_biased
    - abseil/profiling/sample_recorder
    - abseil/synchronization/synchronization
    - abseil/time/time
    - abseil/utility/utility
    - abseil/xcprivacy
  - abseil/container/inlined_vector (1.20240116.2):
    - abseil/algorithm/algorithm
    - abseil/base/core_headers
    - abseil/base/throw_delegate
    - abseil/container/inlined_vector_internal
    - abseil/memory/memory
    - abseil/meta/type_traits
    - abseil/xcprivacy
  - abseil/container/inlined_vector_internal (1.20240116.2):
    - abseil/base/config
    - abseil/base/core_headers
    - abseil/container/compressed_tuple
    - abseil/memory/memory
    - abseil/meta/type_traits
    - abseil/types/span
    - abseil/xcprivacy
  - abseil/container/layout (1.20240116.2):
    - abseil/base/config
    - abseil/base/core_headers
    - abseil/debugging/demangle_internal
    - abseil/meta/type_traits
    - abseil/strings/strings
    - abseil/types/span
    - abseil/utility/utility
    - abseil/xcprivacy
  - abseil/container/raw_hash_map (1.20240116.2):
    - abseil/base/config
    - abseil/base/core_headers
    - abseil/base/throw_delegate
    - abseil/container/container_memory
    - abseil/container/raw_hash_set
    - abseil/xcprivacy
  - abseil/container/raw_hash_set (1.20240116.2):
    - abseil/base/config
    - abseil/base/core_headers
    - abseil/base/dynamic_annotations
    - abseil/base/endian
    - abseil/base/prefetch
    - abseil/base/raw_logging_internal
    - abseil/container/common
    - abseil/container/compressed_tuple
    - abseil/container/container_memory
    - abseil/container/hash_policy_traits
    - abseil/container/hashtable_debug_hooks
    - abseil/container/hashtablez_sampler
    - abseil/hash/hash
    - abseil/memory/memory
    - abseil/meta/type_traits
    - abseil/numeric/bits
    - abseil/utility/utility
    - abseil/xcprivacy
  - abseil/crc/cpu_detect (1.20240116.2):
    - abseil/base/base
    - abseil/base/config
    - abseil/xcprivacy
  - abseil/crc/crc32c (1.20240116.2):
    - abseil/base/config
    - abseil/base/core_headers
    - abseil/base/endian
    - abseil/base/prefetch
    - abseil/crc/cpu_detect
    - abseil/crc/crc_internal
    - abseil/crc/non_temporal_memcpy
    - abseil/strings/str_format
    - abseil/strings/strings
    - abseil/xcprivacy
  - abseil/crc/crc_cord_state (1.20240116.2):
    - abseil/base/config
    - abseil/crc/crc32c
    - abseil/numeric/bits
    - abseil/strings/strings
    - abseil/xcprivacy
  - abseil/crc/crc_internal (1.20240116.2):
    - abseil/base/config
    - abseil/base/core_headers
    - abseil/base/endian
    - abseil/base/prefetch
    - abseil/base/raw_logging_internal
    - abseil/crc/cpu_detect
    - abseil/memory/memory
    - abseil/numeric/bits
    - abseil/xcprivacy
  - abseil/crc/non_temporal_arm_intrinsics (1.20240116.2):
    - abseil/base/config
    - abseil/xcprivacy
  - abseil/crc/non_temporal_memcpy (1.20240116.2):
    - abseil/base/config
    - abseil/base/core_headers
    - abseil/crc/non_temporal_arm_intrinsics
    - abseil/xcprivacy
  - abseil/debugging/debugging_internal (1.20240116.2):
    - abseil/base/config
    - abseil/base/core_headers
    - abseil/base/dynamic_annotations
    - abseil/base/errno_saver
    - abseil/base/raw_logging_internal
    - abseil/xcprivacy
  - abseil/debugging/demangle_internal (1.20240116.2):
    - abseil/base/base
    - abseil/base/config
    - abseil/base/core_headers
    - abseil/xcprivacy
  - abseil/debugging/examine_stack (1.20240116.2):
    - abseil/base/config
    - abseil/base/core_headers
    - abseil/base/raw_logging_internal
    - abseil/debugging/stacktrace
    - abseil/debugging/symbolize
    - abseil/xcprivacy
  - abseil/debugging/stacktrace (1.20240116.2):
    - abseil/base/config
    - abseil/base/core_headers
    - abseil/base/dynamic_annotations
    - abseil/base/raw_logging_internal
    - abseil/debugging/debugging_internal
    - abseil/xcprivacy
  - abseil/debugging/symbolize (1.20240116.2):
    - abseil/base/base
    - abseil/base/config
    - abseil/base/core_headers
    - abseil/base/dynamic_annotations
    - abseil/base/malloc_internal
    - abseil/base/raw_logging_internal
    - abseil/debugging/debugging_internal
    - abseil/debugging/demangle_internal
    - abseil/strings/strings
    - abseil/xcprivacy
  - abseil/flags/commandlineflag (1.20240116.2):
    - abseil/base/config
    - abseil/base/fast_type_id
    - abseil/flags/commandlineflag_internal
    - abseil/strings/strings
    - abseil/types/optional
    - abseil/xcprivacy
  - abseil/flags/commandlineflag_internal (1.20240116.2):
    - abseil/base/config
    - abseil/base/fast_type_id
    - abseil/xcprivacy
  - abseil/flags/config (1.20240116.2):
    - abseil/base/config
    - abseil/base/core_headers
    - abseil/flags/path_util
    - abseil/flags/program_name
    - abseil/strings/strings
    - abseil/synchronization/synchronization
    - abseil/xcprivacy
  - abseil/flags/flag (1.20240116.2):
    - abseil/base/base
    - abseil/base/config
    - abseil/base/core_headers
    - abseil/flags/config
    - abseil/flags/flag_internal
    - abseil/flags/reflection
    - abseil/strings/strings
    - abseil/xcprivacy
  - abseil/flags/flag_internal (1.20240116.2):
    - abseil/base/base
    - abseil/base/config
    - abseil/base/core_headers
    - abseil/base/dynamic_annotations
    - abseil/flags/commandlineflag
    - abseil/flags/commandlineflag_internal
    - abseil/flags/config
    - abseil/flags/marshalling
    - abseil/flags/reflection
    - abseil/memory/memory
    - abseil/meta/type_traits
    - abseil/strings/strings
    - abseil/synchronization/synchronization
    - abseil/utility/utility
    - abseil/xcprivacy
  - abseil/flags/marshalling (1.20240116.2):
    - abseil/base/config
    - abseil/base/core_headers
    - abseil/base/log_severity
    - abseil/numeric/int128
    - abseil/strings/str_format
    - abseil/strings/strings
    - abseil/types/optional
    - abseil/xcprivacy
  - abseil/flags/path_util (1.20240116.2):
    - abseil/base/config
    - abseil/strings/strings
    - abseil/xcprivacy
  - abseil/flags/private_handle_accessor (1.20240116.2):
    - abseil/base/config
    - abseil/flags/commandlineflag
    - abseil/flags/commandlineflag_internal
    - abseil/strings/strings
    - abseil/xcprivacy
  - abseil/flags/program_name (1.20240116.2):
    - abseil/base/config
    - abseil/base/core_headers
    - abseil/flags/path_util
    - abseil/strings/strings
    - abseil/synchronization/synchronization
    - abseil/xcprivacy
  - abseil/flags/reflection (1.20240116.2):
    - abseil/base/config
    - abseil/base/core_headers
    - abseil/base/no_destructor
    - abseil/container/flat_hash_map
    - abseil/flags/commandlineflag
    - abseil/flags/commandlineflag_internal
    - abseil/flags/config
    - abseil/flags/private_handle_accessor
    - abseil/strings/strings
    - abseil/synchronization/synchronization
    - abseil/xcprivacy
  - abseil/functional/any_invocable (1.20240116.2):
    - abseil/base/base_internal
    - abseil/base/config
    - abseil/base/core_headers
    - abseil/meta/type_traits
    - abseil/utility/utility
    - abseil/xcprivacy
  - abseil/functional/bind_front (1.20240116.2):
    - abseil/base/base_internal
    - abseil/container/compressed_tuple
    - abseil/meta/type_traits
    - abseil/utility/utility
    - abseil/xcprivacy
  - abseil/functional/function_ref (1.20240116.2):
    - abseil/base/base_internal
    - abseil/base/core_headers
    - abseil/functional/any_invocable
    - abseil/meta/type_traits
    - abseil/xcprivacy
  - abseil/hash/city (1.20240116.2):
    - abseil/base/config
    - abseil/base/core_headers
    - abseil/base/endian
    - abseil/xcprivacy
  - abseil/hash/hash (1.20240116.2):
    - abseil/base/config
    - abseil/base/core_headers
    - abseil/base/endian
    - abseil/container/fixed_array
    - abseil/functional/function_ref
    - abseil/hash/city
    - abseil/hash/low_level_hash
    - abseil/meta/type_traits
    - abseil/numeric/bits
    - abseil/numeric/int128
    - abseil/strings/strings
    - abseil/types/optional
    - abseil/types/variant
    - abseil/utility/utility
    - abseil/xcprivacy
  - abseil/hash/low_level_hash (1.20240116.2):
    - abseil/base/config
    - abseil/base/endian
    - abseil/base/prefetch
    - abseil/numeric/int128
    - abseil/xcprivacy
  - abseil/log/absl_check (1.20240116.2):
    - abseil/log/internal/check_impl
    - abseil/xcprivacy
  - abseil/log/absl_log (1.20240116.2):
    - abseil/log/internal/log_impl
    - abseil/xcprivacy
  - abseil/log/absl_vlog_is_on (1.20240116.2):
    - abseil/base/config
    - abseil/base/core_headers
    - abseil/log/internal/vlog_config
    - abseil/strings/strings
    - abseil/xcprivacy
  - abseil/log/check (1.20240116.2):
    - abseil/log/internal/check_impl
    - abseil/log/internal/check_op
    - abseil/log/internal/conditions
    - abseil/log/internal/log_message
    - abseil/log/internal/strip
    - abseil/xcprivacy
  - abseil/log/globals (1.20240116.2):
    - abseil/base/atomic_hook
    - abseil/base/config
    - abseil/base/core_headers
    - abseil/base/log_severity
    - abseil/base/raw_logging_internal
    - abseil/hash/hash
    - abseil/log/internal/vlog_config
    - abseil/strings/strings
    - abseil/xcprivacy
  - abseil/log/internal/append_truncated (1.20240116.2):
    - abseil/base/config
    - abseil/strings/strings
    - abseil/types/span
    - abseil/xcprivacy
  - abseil/log/internal/check_impl (1.20240116.2):
    - abseil/base/core_headers
    - abseil/log/internal/check_op
    - abseil/log/internal/conditions
    - abseil/log/internal/log_message
    - abseil/log/internal/strip
    - abseil/xcprivacy
  - abseil/log/internal/check_op (1.20240116.2):
    - abseil/base/config
    - abseil/base/core_headers
    - abseil/log/internal/nullguard
    - abseil/log/internal/nullstream
    - abseil/log/internal/strip
    - abseil/strings/strings
    - abseil/xcprivacy
  - abseil/log/internal/conditions (1.20240116.2):
    - abseil/base/base
    - abseil/base/config
    - abseil/base/core_headers
    - abseil/log/internal/voidify
    - abseil/xcprivacy
  - abseil/log/internal/config (1.20240116.2):
    - abseil/base/config
    - abseil/base/core_headers
    - abseil/xcprivacy
  - abseil/log/internal/fnmatch (1.20240116.2):
    - abseil/base/config
    - abseil/strings/strings
    - abseil/xcprivacy
  - abseil/log/internal/format (1.20240116.2):
    - abseil/base/config
    - abseil/base/core_headers
    - abseil/base/log_severity
    - abseil/log/internal/append_truncated
    - abseil/log/internal/config
    - abseil/log/internal/globals
    - abseil/strings/str_format
    - abseil/strings/strings
    - abseil/time/time
    - abseil/types/span
    - abseil/xcprivacy
  - abseil/log/internal/globals (1.20240116.2):
    - abseil/base/config
    - abseil/base/core_headers
    - abseil/base/log_severity
    - abseil/base/raw_logging_internal
    - abseil/strings/strings
    - abseil/time/time
    - abseil/xcprivacy
  - abseil/log/internal/log_impl (1.20240116.2):
    - abseil/log/absl_vlog_is_on
    - abseil/log/internal/conditions
    - abseil/log/internal/log_message
    - abseil/log/internal/strip
    - abseil/xcprivacy
  - abseil/log/internal/log_message (1.20240116.2):
    - abseil/base/base
    - abseil/base/config
    - abseil/base/core_headers
    - abseil/base/errno_saver
    - abseil/base/log_severity
    - abseil/base/raw_logging_internal
    - abseil/base/strerror
    - abseil/container/inlined_vector
    - abseil/debugging/examine_stack
    - abseil/log/globals
    - abseil/log/internal/append_truncated
    - abseil/log/internal/format
    - abseil/log/internal/globals
    - abseil/log/internal/log_sink_set
    - abseil/log/internal/nullguard
    - abseil/log/internal/proto
    - abseil/log/log_entry
    - abseil/log/log_sink
    - abseil/log/log_sink_registry
    - abseil/memory/memory
    - abseil/strings/strings
    - abseil/time/time
    - abseil/types/span
    - abseil/xcprivacy
  - abseil/log/internal/log_sink_set (1.20240116.2):
    - abseil/base/base
    - abseil/base/config
    - abseil/base/core_headers
    - abseil/base/log_severity
    - abseil/base/no_destructor
    - abseil/base/raw_logging_internal
    - abseil/cleanup/cleanup
    - abseil/log/globals
    - abseil/log/internal/config
    - abseil/log/internal/globals
    - abseil/log/log_entry
    - abseil/log/log_sink
    - abseil/strings/strings
    - abseil/synchronization/synchronization
    - abseil/types/span
    - abseil/xcprivacy
  - abseil/log/internal/nullguard (1.20240116.2):
    - abseil/base/config
    - abseil/base/core_headers
    - abseil/xcprivacy
  - abseil/log/internal/nullstream (1.20240116.2):
    - abseil/base/config
    - abseil/base/core_headers
    - abseil/base/log_severity
    - abseil/strings/strings
    - abseil/xcprivacy
  - abseil/log/internal/proto (1.20240116.2):
    - abseil/base/base
    - abseil/base/config
    - abseil/base/core_headers
    - abseil/strings/strings
    - abseil/types/span
    - abseil/xcprivacy
  - abseil/log/internal/strip (1.20240116.2):
    - abseil/base/log_severity
    - abseil/log/internal/log_message
    - abseil/log/internal/nullstream
    - abseil/xcprivacy
  - abseil/log/internal/vlog_config (1.20240116.2):
    - abseil/base/base
    - abseil/base/config
    - abseil/base/core_headers
    - abseil/base/no_destructor
    - abseil/log/internal/fnmatch
    - abseil/memory/memory
    - abseil/strings/strings
    - abseil/synchronization/synchronization
    - abseil/types/optional
    - abseil/xcprivacy
  - abseil/log/internal/voidify (1.20240116.2):
    - abseil/base/config
    - abseil/xcprivacy
  - abseil/log/log (1.20240116.2):
    - abseil/log/internal/log_impl
    - abseil/log/vlog_is_on
    - abseil/xcprivacy
  - abseil/log/log_entry (1.20240116.2):
    - abseil/base/config
    - abseil/base/core_headers
    - abseil/base/log_severity
    - abseil/log/internal/config
    - abseil/strings/strings
    - abseil/time/time
    - abseil/types/span
    - abseil/xcprivacy
  - abseil/log/log_sink (1.20240116.2):
    - abseil/base/config
    - abseil/log/log_entry
    - abseil/xcprivacy
  - abseil/log/log_sink_registry (1.20240116.2):
    - abseil/base/config
    - abseil/log/internal/log_sink_set
    - abseil/log/log_sink
    - abseil/xcprivacy
  - abseil/log/vlog_is_on (1.20240116.2):
    - abseil/log/absl_vlog_is_on
    - abseil/xcprivacy
  - abseil/memory (1.20240116.2):
    - abseil/memory/memory (= 1.20240116.2)
  - abseil/memory/memory (1.20240116.2):
    - abseil/base/core_headers
    - abseil/meta/type_traits
    - abseil/xcprivacy
  - abseil/meta (1.20240116.2):
    - abseil/meta/type_traits (= 1.20240116.2)
  - abseil/meta/type_traits (1.20240116.2):
    - abseil/base/config
    - abseil/base/core_headers
    - abseil/xcprivacy
  - abseil/numeric/bits (1.20240116.2):
    - abseil/base/config
    - abseil/base/core_headers
    - abseil/xcprivacy
  - abseil/numeric/int128 (1.20240116.2):
    - abseil/base/config
    - abseil/base/core_headers
    - abseil/numeric/bits
    - abseil/xcprivacy
  - abseil/numeric/representation (1.20240116.2):
    - abseil/base/config
    - abseil/xcprivacy
  - abseil/profiling/exponential_biased (1.20240116.2):
    - abseil/base/config
    - abseil/base/core_headers
    - abseil/xcprivacy
  - abseil/profiling/sample_recorder (1.20240116.2):
    - abseil/base/config
    - abseil/base/core_headers
    - abseil/synchronization/synchronization
    - abseil/time/time
    - abseil/xcprivacy
  - abseil/random/bit_gen_ref (1.20240116.2):
    - abseil/base/core_headers
    - abseil/base/fast_type_id
    - abseil/meta/type_traits
    - abseil/random/internal/distribution_caller
    - abseil/random/internal/fast_uniform_bits
    - abseil/random/random
    - abseil/xcprivacy
  - abseil/random/distributions (1.20240116.2):
    - abseil/base/base_internal
    - abseil/base/config
    - abseil/base/core_headers
    - abseil/meta/type_traits
    - abseil/numeric/bits
    - abseil/random/internal/distribution_caller
    - abseil/random/internal/fast_uniform_bits
    - abseil/random/internal/fastmath
    - abseil/random/internal/generate_real
    - abseil/random/internal/iostream_state_saver
    - abseil/random/internal/traits
    - abseil/random/internal/uniform_helper
    - abseil/random/internal/wide_multiply
    - abseil/strings/strings
    - abseil/xcprivacy
  - abseil/random/internal/distribution_caller (1.20240116.2):
    - abseil/base/config
    - abseil/base/fast_type_id
    - abseil/utility/utility
    - abseil/xcprivacy
  - abseil/random/internal/fast_uniform_bits (1.20240116.2):
    - abseil/base/config
    - abseil/meta/type_traits
    - abseil/random/internal/traits
    - abseil/xcprivacy
  - abseil/random/internal/fastmath (1.20240116.2):
    - abseil/numeric/bits
    - abseil/xcprivacy
  - abseil/random/internal/generate_real (1.20240116.2):
    - abseil/meta/type_traits
    - abseil/numeric/bits
    - abseil/random/internal/fastmath
    - abseil/random/internal/traits
    - abseil/xcprivacy
  - abseil/random/internal/iostream_state_saver (1.20240116.2):
    - abseil/meta/type_traits
    - abseil/numeric/int128
    - abseil/xcprivacy
  - abseil/random/internal/nonsecure_base (1.20240116.2):
    - abseil/base/core_headers
    - abseil/container/inlined_vector
    - abseil/meta/type_traits
    - abseil/random/internal/pool_urbg
    - abseil/random/internal/salted_seed_seq
    - abseil/random/internal/seed_material
    - abseil/types/span
    - abseil/xcprivacy
  - abseil/random/internal/pcg_engine (1.20240116.2):
    - abseil/base/config
    - abseil/meta/type_traits
    - abseil/numeric/bits
    - abseil/numeric/int128
    - abseil/random/internal/fastmath
    - abseil/random/internal/iostream_state_saver
    - abseil/xcprivacy
  - abseil/random/internal/platform (1.20240116.2):
    - abseil/base/config
    - abseil/xcprivacy
  - abseil/random/internal/pool_urbg (1.20240116.2):
    - abseil/base/base
    - abseil/base/config
    - abseil/base/core_headers
    - abseil/base/endian
    - abseil/base/raw_logging_internal
    - abseil/random/internal/randen
    - abseil/random/internal/seed_material
    - abseil/random/internal/traits
    - abseil/random/seed_gen_exception
    - abseil/types/span
    - abseil/xcprivacy
  - abseil/random/internal/randen (1.20240116.2):
    - abseil/base/raw_logging_internal
    - abseil/random/internal/platform
    - abseil/random/internal/randen_hwaes
    - abseil/random/internal/randen_slow
    - abseil/xcprivacy
  - abseil/random/internal/randen_engine (1.20240116.2):
    - abseil/base/endian
    - abseil/meta/type_traits
    - abseil/random/internal/iostream_state_saver
    - abseil/random/internal/randen
    - abseil/xcprivacy
  - abseil/random/internal/randen_hwaes (1.20240116.2):
    - abseil/base/config
    - abseil/random/internal/platform
    - abseil/random/internal/randen_hwaes_impl
    - abseil/xcprivacy
  - abseil/random/internal/randen_hwaes_impl (1.20240116.2):
    - abseil/base/config
    - abseil/base/core_headers
    - abseil/numeric/int128
    - abseil/random/internal/platform
    - abseil/xcprivacy
  - abseil/random/internal/randen_slow (1.20240116.2):
    - abseil/base/config
    - abseil/base/core_headers
    - abseil/base/endian
    - abseil/numeric/int128
    - abseil/random/internal/platform
    - abseil/xcprivacy
  - abseil/random/internal/salted_seed_seq (1.20240116.2):
    - abseil/container/inlined_vector
    - abseil/meta/type_traits
    - abseil/random/internal/seed_material
    - abseil/types/optional
    - abseil/types/span
    - abseil/xcprivacy
  - abseil/random/internal/seed_material (1.20240116.2):
    - abseil/base/core_headers
    - abseil/base/dynamic_annotations
    - abseil/base/raw_logging_internal
    - abseil/random/internal/fast_uniform_bits
    - abseil/strings/strings
    - abseil/types/optional
    - abseil/types/span
    - abseil/xcprivacy
  - abseil/random/internal/traits (1.20240116.2):
    - abseil/base/config
    - abseil/numeric/bits
    - abseil/numeric/int128
    - abseil/xcprivacy
  - abseil/random/internal/uniform_helper (1.20240116.2):
    - abseil/base/config
    - abseil/meta/type_traits
    - abseil/numeric/int128
    - abseil/random/internal/traits
    - abseil/xcprivacy
  - abseil/random/internal/wide_multiply (1.20240116.2):
    - abseil/base/config
    - abseil/numeric/bits
    - abseil/numeric/int128
    - abseil/random/internal/traits
    - abseil/xcprivacy
  - abseil/random/random (1.20240116.2):
    - abseil/random/distributions
    - abseil/random/internal/nonsecure_base
    - abseil/random/internal/pcg_engine
    - abseil/random/internal/pool_urbg
    - abseil/random/internal/randen_engine
    - abseil/random/seed_sequences
    - abseil/xcprivacy
  - abseil/random/seed_gen_exception (1.20240116.2):
    - abseil/base/config
    - abseil/xcprivacy
  - abseil/random/seed_sequences (1.20240116.2):
    - abseil/base/config
    - abseil/random/internal/pool_urbg
    - abseil/random/internal/salted_seed_seq
    - abseil/random/internal/seed_material
    - abseil/random/seed_gen_exception
    - abseil/types/span
    - abseil/xcprivacy
  - abseil/status/status (1.20240116.2):
    - abseil/base/atomic_hook
    - abseil/base/config
    - abseil/base/core_headers
    - abseil/base/no_destructor
    - abseil/base/nullability
    - abseil/base/raw_logging_internal
    - abseil/base/strerror
    - abseil/container/inlined_vector
    - abseil/debugging/stacktrace
    - abseil/debugging/symbolize
    - abseil/functional/function_ref
    - abseil/memory/memory
    - abseil/strings/cord
    - abseil/strings/str_format
    - abseil/strings/strings
    - abseil/types/optional
    - abseil/types/span
    - abseil/xcprivacy
  - abseil/status/statusor (1.20240116.2):
    - abseil/base/base
    - abseil/base/config
    - abseil/base/core_headers
    - abseil/base/nullability
    - abseil/base/raw_logging_internal
    - abseil/meta/type_traits
    - abseil/status/status
    - abseil/strings/has_ostream_operator
    - abseil/strings/str_format
    - abseil/strings/strings
    - abseil/types/variant
    - abseil/utility/utility
    - abseil/xcprivacy
  - abseil/strings/charset (1.20240116.2):
    - abseil/base/core_headers
    - abseil/strings/string_view
    - abseil/xcprivacy
  - abseil/strings/cord (1.20240116.2):
    - abseil/base/base
    - abseil/base/config
    - abseil/base/core_headers
    - abseil/base/endian
    - abseil/base/nullability
    - abseil/base/raw_logging_internal
    - abseil/container/inlined_vector
    - abseil/crc/crc32c
    - abseil/crc/crc_cord_state
    - abseil/functional/function_ref
    - abseil/meta/type_traits
    - abseil/numeric/bits
    - abseil/strings/cord_internal
    - abseil/strings/cordz_functions
    - abseil/strings/cordz_info
    - abseil/strings/cordz_statistics
    - abseil/strings/cordz_update_scope
    - abseil/strings/cordz_update_tracker
    - abseil/strings/internal
    - abseil/strings/strings
    - abseil/types/optional
    - abseil/types/span
    - abseil/xcprivacy
  - abseil/strings/cord_internal (1.20240116.2):
    - abseil/base/base_internal
    - abseil/base/config
    - abseil/base/core_headers
    - abseil/base/endian
    - abseil/base/raw_logging_internal
    - abseil/base/throw_delegate
    - abseil/container/compressed_tuple
    - abseil/container/container_memory
    - abseil/container/inlined_vector
    - abseil/container/layout
    - abseil/crc/crc_cord_state
    - abseil/functional/function_ref
    - abseil/meta/type_traits
    - abseil/strings/strings
    - abseil/types/span
    - abseil/xcprivacy
  - abseil/strings/cordz_functions (1.20240116.2):
    - abseil/base/config
    - abseil/base/core_headers
    - abseil/base/raw_logging_internal
    - abseil/profiling/exponential_biased
    - abseil/xcprivacy
  - abseil/strings/cordz_handle (1.20240116.2):
    - abseil/base/base
    - abseil/base/config
    - abseil/base/raw_logging_internal
    - abseil/synchronization/synchronization
    - abseil/xcprivacy
  - abseil/strings/cordz_info (1.20240116.2):
    - abseil/base/base
    - abseil/base/config
    - abseil/base/core_headers
    - abseil/base/raw_logging_internal
    - abseil/container/inlined_vector
    - abseil/debugging/stacktrace
    - abseil/strings/cord_internal
    - abseil/strings/cordz_functions
    - abseil/strings/cordz_handle
    - abseil/strings/cordz_statistics
    - abseil/strings/cordz_update_tracker
    - abseil/synchronization/synchronization
    - abseil/time/time
    - abseil/types/span
    - abseil/xcprivacy
  - abseil/strings/cordz_statistics (1.20240116.2):
    - abseil/base/config
    - abseil/strings/cordz_update_tracker
    - abseil/xcprivacy
  - abseil/strings/cordz_update_scope (1.20240116.2):
    - abseil/base/config
    - abseil/base/core_headers
    - abseil/strings/cord_internal
    - abseil/strings/cordz_info
    - abseil/strings/cordz_update_tracker
    - abseil/xcprivacy
  - abseil/strings/cordz_update_tracker (1.20240116.2):
    - abseil/base/config
    - abseil/xcprivacy
  - abseil/strings/has_ostream_operator (1.20240116.2):
    - abseil/base/config
    - abseil/xcprivacy
  - abseil/strings/internal (1.20240116.2):
    - abseil/base/config
    - abseil/base/core_headers
    - abseil/base/endian
    - abseil/base/raw_logging_internal
    - abseil/meta/type_traits
    - abseil/xcprivacy
  - abseil/strings/str_format (1.20240116.2):
    - abseil/base/config
    - abseil/base/core_headers
    - abseil/base/nullability
    - abseil/strings/str_format_internal
    - abseil/strings/string_view
    - abseil/types/span
    - abseil/xcprivacy
  - abseil/strings/str_format_internal (1.20240116.2):
    - abseil/base/config
    - abseil/base/core_headers
    - abseil/container/fixed_array
    - abseil/container/inlined_vector
    - abseil/functional/function_ref
    - abseil/meta/type_traits
    - abseil/numeric/bits
    - abseil/numeric/int128
    - abseil/numeric/representation
    - abseil/strings/strings
    - abseil/types/optional
    - abseil/types/span
    - abseil/utility/utility
    - abseil/xcprivacy
  - abseil/strings/string_view (1.20240116.2):
    - abseil/base/base
    - abseil/base/config
    - abseil/base/core_headers
    - abseil/base/nullability
    - abseil/base/throw_delegate
    - abseil/xcprivacy
  - abseil/strings/strings (1.20240116.2):
    - abseil/base/base
    - abseil/base/config
    - abseil/base/core_headers
    - abseil/base/endian
    - abseil/base/nullability
    - abseil/base/raw_logging_internal
    - abseil/base/throw_delegate
    - abseil/memory/memory
    - abseil/meta/type_traits
    - abseil/numeric/bits
    - abseil/numeric/int128
    - abseil/strings/charset
    - abseil/strings/internal
    - abseil/strings/string_view
    - abseil/xcprivacy
  - abseil/synchronization/graphcycles_internal (1.20240116.2):
    - abseil/base/base
    - abseil/base/base_internal
    - abseil/base/config
    - abseil/base/core_headers
    - abseil/base/malloc_internal
    - abseil/base/raw_logging_internal
    - abseil/xcprivacy
  - abseil/synchronization/kernel_timeout_internal (1.20240116.2):
    - abseil/base/base
    - abseil/base/config
    - abseil/base/core_headers
    - abseil/base/raw_logging_internal
    - abseil/time/time
    - abseil/xcprivacy
  - abseil/synchronization/synchronization (1.20240116.2):
    - abseil/base/atomic_hook
    - abseil/base/base
    - abseil/base/base_internal
    - abseil/base/config
    - abseil/base/core_headers
    - abseil/base/dynamic_annotations
    - abseil/base/malloc_internal
    - abseil/base/raw_logging_internal
    - abseil/debugging/stacktrace
    - abseil/debugging/symbolize
    - abseil/synchronization/graphcycles_internal
    - abseil/synchronization/kernel_timeout_internal
    - abseil/time/time
    - abseil/xcprivacy
  - abseil/time (1.20240116.2):
    - abseil/time/internal (= 1.20240116.2)
    - abseil/time/time (= 1.20240116.2)
  - abseil/time/internal (1.20240116.2):
    - abseil/time/internal/cctz (= 1.20240116.2)
  - abseil/time/internal/cctz (1.20240116.2):
    - abseil/time/internal/cctz/civil_time (= 1.20240116.2)
    - abseil/time/internal/cctz/time_zone (= 1.20240116.2)
  - abseil/time/internal/cctz/civil_time (1.20240116.2):
    - abseil/base/config
    - abseil/xcprivacy
  - abseil/time/internal/cctz/time_zone (1.20240116.2):
    - abseil/base/config
    - abseil/time/internal/cctz/civil_time
    - abseil/xcprivacy
  - abseil/time/time (1.20240116.2):
    - abseil/base/base
    - abseil/base/config
    - abseil/base/core_headers
    - abseil/base/raw_logging_internal
    - abseil/numeric/int128
    - abseil/strings/strings
    - abseil/time/internal/cctz/civil_time
    - abseil/time/internal/cctz/time_zone
    - abseil/types/optional
    - abseil/xcprivacy
  - abseil/types (1.20240116.2):
    - abseil/types/any (= 1.20240116.2)
    - abseil/types/bad_any_cast (= 1.20240116.2)
    - abseil/types/bad_any_cast_impl (= 1.20240116.2)
    - abseil/types/bad_optional_access (= 1.20240116.2)
    - abseil/types/bad_variant_access (= 1.20240116.2)
    - abseil/types/compare (= 1.20240116.2)
    - abseil/types/optional (= 1.20240116.2)
    - abseil/types/span (= 1.20240116.2)
    - abseil/types/variant (= 1.20240116.2)
  - abseil/types/any (1.20240116.2):
    - abseil/base/config
    - abseil/base/core_headers
    - abseil/base/fast_type_id
    - abseil/meta/type_traits
    - abseil/types/bad_any_cast
    - abseil/utility/utility
    - abseil/xcprivacy
  - abseil/types/bad_any_cast (1.20240116.2):
    - abseil/base/config
    - abseil/types/bad_any_cast_impl
    - abseil/xcprivacy
  - abseil/types/bad_any_cast_impl (1.20240116.2):
    - abseil/base/config
    - abseil/base/raw_logging_internal
    - abseil/xcprivacy
  - abseil/types/bad_optional_access (1.20240116.2):
    - abseil/base/config
    - abseil/base/raw_logging_internal
    - abseil/xcprivacy
  - abseil/types/bad_variant_access (1.20240116.2):
    - abseil/base/config
    - abseil/base/raw_logging_internal
    - abseil/xcprivacy
  - abseil/types/compare (1.20240116.2):
    - abseil/base/config
    - abseil/base/core_headers
    - abseil/meta/type_traits
    - abseil/xcprivacy
  - abseil/types/optional (1.20240116.2):
    - abseil/base/base_internal
    - abseil/base/config
    - abseil/base/core_headers
    - abseil/base/nullability
    - abseil/memory/memory
    - abseil/meta/type_traits
    - abseil/types/bad_optional_access
    - abseil/utility/utility
    - abseil/xcprivacy
  - abseil/types/span (1.20240116.2):
    - abseil/algorithm/algorithm
    - abseil/base/core_headers
    - abseil/base/nullability
    - abseil/base/throw_delegate
    - abseil/meta/type_traits
    - abseil/xcprivacy
  - abseil/types/variant (1.20240116.2):
    - abseil/base/base_internal
    - abseil/base/config
    - abseil/base/core_headers
    - abseil/meta/type_traits
    - abseil/types/bad_variant_access
    - abseil/utility/utility
    - abseil/xcprivacy
  - abseil/utility/utility (1.20240116.2):
    - abseil/base/base_internal
    - abseil/base/config
    - abseil/meta/type_traits
    - abseil/xcprivacy
  - abseil/xcprivacy (1.20240116.2)
  - app_links (0.0.2):
    - Flutter
  - AppAuth (1.7.6):
    - AppAuth/Core (= 1.7.6)
    - AppAuth/ExternalUserAgent (= 1.7.6)
  - AppAuth/Core (1.7.6)
  - AppAuth/ExternalUserAgent (1.7.6):
    - AppAuth/Core
  - AppCheckCore (11.2.0):
    - GoogleUtilities/Environment (~> 8.0)
    - GoogleUtilities/UserDefaults (~> 8.0)
    - PromisesObjC (~> 2.4)
  - audio_session (0.0.1):
    - Flutter
  - audioplayers_darwin (0.0.1):
    - Flutter
  - awesome_notifications (0.10.0):
    - Flutter
    - IosAwnCore (~> 0.10.0)
  - BoringSSL-GRPC (0.0.36):
    - BoringSSL-GRPC/Implementation (= 0.0.36)
    - BoringSSL-GRPC/Interface (= 0.0.36)
  - BoringSSL-GRPC/Implementation (0.0.36):
    - BoringSSL-GRPC/Interface (= 0.0.36)
  - BoringSSL-GRPC/Interface (0.0.36)
  - camera_avfoundation (0.0.1):
    - Flutter
  - cloud_firestore (5.6.0):
    - Firebase/Firestore (= 11.6.0)
    - firebase_core
    - Flutter
  - cloud_functions (5.2.0):
    - Firebase/Functions (= 11.6.0)
    - firebase_core
    - Flutter
  - connectivity_plus (0.0.1):
    - Flutter
    - FlutterMacOS
  - desktop_webview_auth (0.0.1):
    - Flutter
  - device_info_plus (0.0.1):
    - Flutter
  - DKImagePickerController/Core (4.3.9):
    - DKImagePickerController/ImageDataManager
    - DKImagePickerController/Resource
  - DKImagePickerController/ImageDataManager (4.3.9)
  - DKImagePickerController/PhotoGallery (4.3.9):
    - DKImagePickerController/Core
    - DKPhotoGallery
  - DKImagePickerController/Resource (4.3.9)
  - DKPhotoGallery (0.0.19):
    - DKPhotoGallery/Core (= 0.0.19)
    - DKPhotoGallery/Model (= 0.0.19)
    - DKPhotoGallery/Preview (= 0.0.19)
    - DKPhotoGallery/Resource (= 0.0.19)
    - SDWebImage
    - SwiftyGif
  - DKPhotoGallery/Core (0.0.19):
    - DKPhotoGallery/Model
    - DKPhotoGallery/Preview
    - SDWebImage
    - SwiftyGif
  - DKPhotoGallery/Model (0.0.19):
    - SDWebImage
    - SwiftyGif
  - DKPhotoGallery/Preview (0.0.19):
    - DKPhotoGallery/Model
    - DKPhotoGallery/Resource
    - SDWebImage
    - SwiftyGif
  - DKPhotoGallery/Resource (0.0.19):
    - SDWebImage
    - SwiftyGif
  - FBAEMKit (16.3.1):
    - FBSDKCoreKit_Basics (= 16.3.1)
  - FBSDKCoreKit (16.3.1):
    - FBAEMKit (= 16.3.1)
    - FBSDKCoreKit_Basics (= 16.3.1)
  - FBSDKCoreKit_Basics (16.3.1)
  - FBSDKLoginKit (16.3.1):
    - FBSDKCoreKit (= 16.3.1)
  - file_picker (0.0.1):
    - DKImagePickerController/PhotoGallery
    - Flutter
  - file_selector_ios (0.0.1):
    - Flutter
  - Firebase/Analytics (11.6.0):
    - Firebase/Core
  - Firebase/Auth (11.6.0):
    - Firebase/CoreOnly
    - FirebaseAuth (~> 11.6.0)
  - Firebase/Core (11.6.0):
    - Firebase/CoreOnly
    - FirebaseAnalytics (~> 11.6.0)
  - Firebase/CoreOnly (11.6.0):
    - FirebaseCore (~> 11.6.0)
  - Firebase/Crashlytics (11.6.0):
    - Firebase/CoreOnly
    - FirebaseCrashlytics (~> 11.6.0)
  - Firebase/Database (11.6.0):
    - Firebase/CoreOnly
    - FirebaseDatabase (~> 11.6.0)
  - Firebase/DynamicLinks (11.6.0):
    - Firebase/CoreOnly
    - FirebaseDynamicLinks (~> 11.6.0)
  - Firebase/Firestore (11.6.0):
    - Firebase/CoreOnly
    - FirebaseFirestore (~> 11.6.0)
  - Firebase/Functions (11.6.0):
    - Firebase/CoreOnly
    - FirebaseFunctions (~> 11.6.0)
  - Firebase/Messaging (11.6.0):
    - Firebase/CoreOnly
    - FirebaseMessaging (~> 11.6.0)
  - Firebase/MLModelDownloader (11.6.0):
    - Firebase/CoreOnly
    - FirebaseMLModelDownloader (~> 11.6.0-beta)
  - Firebase/Performance (11.6.0):
    - Firebase/CoreOnly
    - FirebasePerformance (~> 11.6.0)
  - Firebase/RemoteConfig (11.6.0):
    - Firebase/CoreOnly
    - FirebaseRemoteConfig (~> 11.6.0)
  - Firebase/Storage (11.6.0):
    - Firebase/CoreOnly
    - FirebaseStorage (~> 11.6.0)
  - firebase_analytics (11.3.6):
    - Firebase/Analytics (= 11.6.0)
    - firebase_core
    - Flutter
  - firebase_app_check (0.3.2):
    - Firebase/CoreOnly (~> 11.6.0)
    - firebase_core
    - FirebaseAppCheck (~> 11.6.0)
    - Flutter
  - firebase_auth (5.4.0):
    - Firebase/Auth (= 11.6.0)
    - firebase_core
    - Flutter
  - firebase_core (3.10.0):
    - Firebase/CoreOnly (= 11.6.0)
    - Flutter
  - firebase_crashlytics (4.3.0):
    - Firebase/Crashlytics (= 11.6.0)
    - firebase_core
    - Flutter
  - firebase_database (11.2.0):
    - Firebase/Database (= 11.6.0)
    - firebase_core
    - Flutter
  - firebase_dynamic_links (6.1.0):
    - Firebase/DynamicLinks (= 11.6.0)
    - firebase_core
    - Flutter
  - firebase_messaging (15.1.6):
    - Firebase/Messaging (= 11.6.0)
    - firebase_core
    - Flutter
  - firebase_ml_model_downloader (0.3.1-6):
    - Firebase/MLModelDownloader (= 11.6.0)
    - firebase_core
    - Flutter
  - firebase_performance (0.10.0-11):
    - Firebase/Performance (= 11.6.0)
    - firebase_core
    - Flutter
  - firebase_remote_config (5.2.0):
    - Firebase/RemoteConfig (= 11.6.0)
    - firebase_core
    - Flutter
  - firebase_storage (12.3.7):
    - Firebase/Storage (= 11.6.0)
    - firebase_core
    - Flutter
  - FirebaseABTesting (11.6.0):
    - FirebaseCore (~> 11.6.0)
  - FirebaseAnalytics (11.6.0):
    - FirebaseAnalytics/AdIdSupport (= 11.6.0)
    - FirebaseCore (~> 11.6.0)
    - FirebaseInstallations (~> 11.0)
    - GoogleUtilities/AppDelegateSwizzler (~> 8.0)
    - GoogleUtilities/MethodSwizzler (~> 8.0)
    - GoogleUtilities/Network (~> 8.0)
    - "GoogleUtilities/NSData+zlib (~> 8.0)"
    - nanopb (~> 3.30910.0)
  - FirebaseAnalytics/AdIdSupport (11.6.0):
    - FirebaseCore (~> 11.6.0)
    - FirebaseInstallations (~> 11.0)
    - GoogleAppMeasurement (= 11.6.0)
    - GoogleUtilities/AppDelegateSwizzler (~> 8.0)
    - GoogleUtilities/MethodSwizzler (~> 8.0)
    - GoogleUtilities/Network (~> 8.0)
    - "GoogleUtilities/NSData+zlib (~> 8.0)"
    - nanopb (~> 3.30910.0)
  - FirebaseAppCheck (11.6.0):
    - AppCheckCore (~> 11.0)
    - FirebaseAppCheckInterop (~> 11.0)
    - FirebaseCore (~> 11.6.0)
    - GoogleUtilities/Environment (~> 8.0)
    - GoogleUtilities/UserDefaults (~> 8.0)
  - FirebaseAppCheckInterop (11.13.0)
  - FirebaseAuth (11.6.0):
    - FirebaseAppCheckInterop (~> 11.0)
    - FirebaseAuthInterop (~> 11.0)
    - FirebaseCore (~> 11.6.0)
    - FirebaseCoreExtension (~> 11.6.0)
    - GoogleUtilities/AppDelegateSwizzler (~> 8.0)
    - GoogleUtilities/Environment (~> 8.0)
    - GTMSessionFetcher/Core (< 5.0, >= 3.4)
    - RecaptchaInterop (~> 100.0)
  - FirebaseAuthInterop (11.13.0)
  - FirebaseCore (11.6.0):
    - FirebaseCoreInternal (~> 11.6.0)
    - GoogleUtilities/Environment (~> 8.0)
    - GoogleUtilities/Logger (~> 8.0)
  - FirebaseCoreExtension (11.6.0):
    - FirebaseCore (~> 11.6.0)
  - FirebaseCoreInternal (11.6.0):
    - "GoogleUtilities/NSData+zlib (~> 8.0)"
  - FirebaseCrashlytics (11.6.0):
    - FirebaseCore (~> 11.6.0)
    - FirebaseInstallations (~> 11.0)
    - FirebaseRemoteConfigInterop (~> 11.0)
    - FirebaseSessions (~> 11.0)
    - GoogleDataTransport (~> 10.0)
    - GoogleUtilities/Environment (~> 8.0)
    - nanopb (~> 3.30910.0)
    - PromisesObjC (~> 2.4)
  - FirebaseDatabase (11.6.0):
    - FirebaseAppCheckInterop (~> 11.0)
    - FirebaseCore (~> 11.6.0)
    - FirebaseSharedSwift (~> 11.0)
    - GoogleUtilities/UserDefaults (~> 8.0)
    - leveldb-library (~> 1.22)
  - FirebaseDynamicLinks (11.6.0):
    - FirebaseCore (~> 11.6.0)
  - FirebaseFirestore (11.6.0):
    - FirebaseCore (~> 11.6.0)
    - FirebaseCoreExtension (~> 11.6.0)
    - FirebaseFirestoreInternal (= 11.6.0)
    - FirebaseSharedSwift (~> 11.0)
  - FirebaseFirestoreInternal (11.6.0):
    - abseil/algorithm (~> 1.20240116.1)
    - abseil/base (~> 1.20240116.1)
    - abseil/container/flat_hash_map (~> 1.20240116.1)
    - abseil/memory (~> 1.20240116.1)
    - abseil/meta (~> 1.20240116.1)
    - abseil/strings/strings (~> 1.20240116.1)
    - abseil/time (~> 1.20240116.1)
    - abseil/types (~> 1.20240116.1)
    - FirebaseAppCheckInterop (~> 11.0)
    - FirebaseCore (~> 11.6.0)
    - "gRPC-C++ (~> 1.65.0)"
    - gRPC-Core (~> 1.65.0)
    - leveldb-library (~> 1.22)
    - nanopb (~> 3.30910.0)
  - FirebaseFunctions (11.6.0):
    - FirebaseAppCheckInterop (~> 11.0)
    - FirebaseAuthInterop (~> 11.0)
    - FirebaseCore (~> 11.6.0)
    - FirebaseCoreExtension (~> 11.6.0)
    - FirebaseMessagingInterop (~> 11.0)
    - FirebaseSharedSwift (~> 11.0)
    - GTMSessionFetcher/Core (< 5.0, >= 3.4)
  - FirebaseInstallations (11.6.0):
    - FirebaseCore (~> 11.6.0)
    - GoogleUtilities/Environment (~> 8.0)
    - GoogleUtilities/UserDefaults (~> 8.0)
    - PromisesObjC (~> 2.4)
  - FirebaseMessaging (11.6.0):
    - FirebaseCore (~> 11.6.0)
    - FirebaseInstallations (~> 11.0)
    - GoogleDataTransport (~> 10.0)
    - GoogleUtilities/AppDelegateSwizzler (~> 8.0)
    - GoogleUtilities/Environment (~> 8.0)
    - GoogleUtilities/Reachability (~> 8.0)
    - GoogleUtilities/UserDefaults (~> 8.0)
    - nanopb (~> 3.30910.0)
  - FirebaseMessagingInterop (11.13.0)
  - FirebaseMLModelDownloader (11.6.0-beta):
    - FirebaseCore (~> 11.6.0)
    - FirebaseCoreExtension (~> 11.6.0)
    - FirebaseInstallations (~> 11.0)
    - GoogleDataTransport (~> 10.0)
    - GoogleUtilities/UserDefaults (~> 8.0)
    - SwiftProtobuf (~> 1.19)
  - FirebasePerformance (11.6.0):
    - FirebaseCore (~> 11.6.0)
    - FirebaseInstallations (~> 11.0)
    - FirebaseRemoteConfig (~> 11.0)
    - FirebaseSessions (~> 11.0)
    - GoogleDataTransport (~> 10.0)
    - GoogleUtilities/Environment (~> 8.0)
    - GoogleUtilities/MethodSwizzler (~> 8.0)
    - GoogleUtilities/UserDefaults (~> 8.0)
    - nanopb (~> 3.30910.0)
  - FirebaseRemoteConfig (11.6.0):
    - FirebaseABTesting (~> 11.0)
    - FirebaseCore (~> 11.6.0)
    - FirebaseInstallations (~> 11.0)
    - FirebaseRemoteConfigInterop (~> 11.0)
    - FirebaseSharedSwift (~> 11.0)
    - GoogleUtilities/Environment (~> 8.0)
    - "GoogleUtilities/NSData+zlib (~> 8.0)"
  - FirebaseRemoteConfigInterop (11.13.0)
  - FirebaseSessions (11.6.0):
    - FirebaseCore (~> 11.6.0)
    - FirebaseCoreExtension (~> 11.6.0)
    - FirebaseInstallations (~> 11.0)
    - GoogleDataTransport (~> 10.0)
    - GoogleUtilities/Environment (~> 8.0)
    - GoogleUtilities/UserDefaults (~> 8.0)
    - nanopb (~> 3.30910.0)
    - PromisesSwift (~> 2.1)
  - FirebaseSharedSwift (11.13.0)
  - FirebaseStorage (11.6.0):
    - FirebaseAppCheckInterop (~> 11.0)
    - FirebaseAuthInterop (~> 11.0)
    - FirebaseCore (~> 11.6.0)
    - FirebaseCoreExtension (~> 11.6.0)
    - GoogleUtilities/Environment (~> 8.0)
    - GTMSessionFetcher/Core (< 5.0, >= 3.4)
  - Flutter (1.0.0)
  - flutter_facebook_auth (6.0.4):
    - FBSDKLoginKit (~> 16.3.1)
    - Flutter
  - flutter_file_dialog (0.0.1):
    - Flutter
  - flutter_gemma (0.0.1):
    - Flutter
    - MediaPipeTasksGenAI (= 0.10.12)
    - MediaPipeTasksGenAIC (= 0.10.12)
  - flutter_icmp_ping (0.0.1):
    - Flutter
  - flutter_inappwebview_ios (0.0.1):
    - Flutter
    - flutter_inappwebview_ios/Core (= 0.0.1)
    - OrderedSet (~> 6.0.3)
  - flutter_inappwebview_ios/Core (0.0.1):
    - Flutter
    - OrderedSet (~> 6.0.3)
  - flutter_keyboard_visibility (0.0.1):
    - Flutter
  - flutter_local_notifications (0.0.1):
    - Flutter
  - flutter_native_splash (2.4.3):
    - Flutter
  - flutter_pdfview (1.0.2):
    - Flutter
  - flutter_stable_diffusion_core_ml (0.0.1):
    - Flutter
    - FlutterMacOS
    - platform_object_channel_foundation
  - flutter_tts (0.0.1):
    - Flutter
  - flutter_vision (0.0.1):
    - Flutter
  - flutter_webrtc (0.12.6):
    - Flutter
    - WebRTC-SDK (= 125.6422.06)
  - gal (1.0.0):
    - Flutter
    - FlutterMacOS
  - geolocator_apple (1.2.0):
    - Flutter
  - ggml_library_flutter (0.0.1):
    - Flutter
  - Google-Maps-iOS-Utils (6.1.0):
    - GoogleMaps (~> 9.0)
  - google_maps_flutter_ios (0.0.1):
    - Flutter
    - Google-Maps-iOS-Utils (< 7.0, >= 5.0)
    - GoogleMaps (< 10.0, >= 8.4)
  - google_sign_in_ios (0.0.1):
    - AppAuth (>= 1.7.4)
    - Flutter
    - FlutterMacOS
    - GoogleSignIn (~> 7.1)
    - GTMSessionFetcher (>= 3.4.0)
  - GoogleAppMeasurement (11.6.0):
    - GoogleAppMeasurement/AdIdSupport (= 11.6.0)
    - GoogleUtilities/AppDelegateSwizzler (~> 8.0)
    - GoogleUtilities/MethodSwizzler (~> 8.0)
    - GoogleUtilities/Network (~> 8.0)
    - "GoogleUtilities/NSData+zlib (~> 8.0)"
    - nanopb (~> 3.30910.0)
  - GoogleAppMeasurement/AdIdSupport (11.6.0):
    - GoogleAppMeasurement/WithoutAdIdSupport (= 11.6.0)
    - GoogleUtilities/AppDelegateSwizzler (~> 8.0)
    - GoogleUtilities/MethodSwizzler (~> 8.0)
    - GoogleUtilities/Network (~> 8.0)
    - "GoogleUtilities/NSData+zlib (~> 8.0)"
    - nanopb (~> 3.30910.0)
  - GoogleAppMeasurement/WithoutAdIdSupport (11.6.0):
    - GoogleUtilities/AppDelegateSwizzler (~> 8.0)
    - GoogleUtilities/MethodSwizzler (~> 8.0)
    - GoogleUtilities/Network (~> 8.0)
    - "GoogleUtilities/NSData+zlib (~> 8.0)"
    - nanopb (~> 3.30910.0)
  - GoogleDataTransport (10.1.0):
    - nanopb (~> 3.30910.0)
    - PromisesObjC (~> 2.4)
  - GoogleMaps (9.4.0):
    - GoogleMaps/Maps (= 9.4.0)
  - GoogleMaps/Maps (9.4.0)
  - GoogleSignIn (7.1.0):
    - AppAuth (< 2.0, >= 1.7.3)
    - GTMAppAuth (< 5.0, >= 4.1.1)
    - GTMSessionFetcher/Core (~> 3.3)
  - GoogleUtilities/AppDelegateSwizzler (8.1.0):
    - GoogleUtilities/Environment
    - GoogleUtilities/Logger
    - GoogleUtilities/Network
    - GoogleUtilities/Privacy
  - GoogleUtilities/Environment (8.1.0):
    - GoogleUtilities/Privacy
  - GoogleUtilities/Logger (8.1.0):
    - GoogleUtilities/Environment
    - GoogleUtilities/Privacy
  - GoogleUtilities/MethodSwizzler (8.1.0):
    - GoogleUtilities/Logger
    - GoogleUtilities/Privacy
  - GoogleUtilities/Network (8.1.0):
    - GoogleUtilities/Logger
    - "GoogleUtilities/NSData+zlib"
    - GoogleUtilities/Privacy
    - GoogleUtilities/Reachability
  - "GoogleUtilities/NSData+zlib (8.1.0)":
    - GoogleUtilities/Privacy
  - GoogleUtilities/Privacy (8.1.0)
  - GoogleUtilities/Reachability (8.1.0):
    - GoogleUtilities/Logger
    - GoogleUtilities/Privacy
  - GoogleUtilities/UserDefaults (8.1.0):
    - GoogleUtilities/Logger
    - GoogleUtilities/Privacy
  - "gRPC-C++ (1.65.5)":
    - "gRPC-C++/Implementation (= 1.65.5)"
    - "gRPC-C++/Interface (= 1.65.5)"
  - "gRPC-C++/Implementation (1.65.5)":
    - abseil/algorithm/container (~> 1.20240116.2)
    - abseil/base/base (~> 1.20240116.2)
    - abseil/base/config (~> 1.20240116.2)
    - abseil/base/core_headers (~> 1.20240116.2)
    - abseil/base/log_severity (~> 1.20240116.2)
    - abseil/base/no_destructor (~> 1.20240116.2)
    - abseil/cleanup/cleanup (~> 1.20240116.2)
    - abseil/container/flat_hash_map (~> 1.20240116.2)
    - abseil/container/flat_hash_set (~> 1.20240116.2)
    - abseil/container/inlined_vector (~> 1.20240116.2)
    - abseil/flags/flag (~> 1.20240116.2)
    - abseil/flags/marshalling (~> 1.20240116.2)
    - abseil/functional/any_invocable (~> 1.20240116.2)
    - abseil/functional/bind_front (~> 1.20240116.2)
    - abseil/functional/function_ref (~> 1.20240116.2)
    - abseil/hash/hash (~> 1.20240116.2)
    - abseil/log/absl_check (~> 1.20240116.2)
    - abseil/log/absl_log (~> 1.20240116.2)
    - abseil/log/check (~> 1.20240116.2)
    - abseil/log/globals (~> 1.20240116.2)
    - abseil/log/log (~> 1.20240116.2)
    - abseil/memory/memory (~> 1.20240116.2)
    - abseil/meta/type_traits (~> 1.20240116.2)
    - abseil/random/bit_gen_ref (~> 1.20240116.2)
    - abseil/random/distributions (~> 1.20240116.2)
    - abseil/random/random (~> 1.20240116.2)
    - abseil/status/status (~> 1.20240116.2)
    - abseil/status/statusor (~> 1.20240116.2)
    - abseil/strings/cord (~> 1.20240116.2)
    - abseil/strings/str_format (~> 1.20240116.2)
    - abseil/strings/strings (~> 1.20240116.2)
    - abseil/synchronization/synchronization (~> 1.20240116.2)
    - abseil/time/time (~> 1.20240116.2)
    - abseil/types/optional (~> 1.20240116.2)
    - abseil/types/span (~> 1.20240116.2)
    - abseil/types/variant (~> 1.20240116.2)
    - abseil/utility/utility (~> 1.20240116.2)
    - "gRPC-C++/Interface (= 1.65.5)"
    - "gRPC-C++/Privacy (= 1.65.5)"
    - gRPC-Core (= 1.65.5)
  - "gRPC-C++/Interface (1.65.5)"
  - "gRPC-C++/Privacy (1.65.5)"
  - gRPC-Core (1.65.5):
    - gRPC-Core/Implementation (= 1.65.5)
    - gRPC-Core/Interface (= 1.65.5)
  - gRPC-Core/Implementation (1.65.5):
    - abseil/algorithm/container (~> 1.20240116.2)
    - abseil/base/base (~> 1.20240116.2)
    - abseil/base/config (~> 1.20240116.2)
    - abseil/base/core_headers (~> 1.20240116.2)
    - abseil/base/log_severity (~> 1.20240116.2)
    - abseil/base/no_destructor (~> 1.20240116.2)
    - abseil/cleanup/cleanup (~> 1.20240116.2)
    - abseil/container/flat_hash_map (~> 1.20240116.2)
    - abseil/container/flat_hash_set (~> 1.20240116.2)
    - abseil/container/inlined_vector (~> 1.20240116.2)
    - abseil/flags/flag (~> 1.20240116.2)
    - abseil/flags/marshalling (~> 1.20240116.2)
    - abseil/functional/any_invocable (~> 1.20240116.2)
    - abseil/functional/bind_front (~> 1.20240116.2)
    - abseil/functional/function_ref (~> 1.20240116.2)
    - abseil/hash/hash (~> 1.20240116.2)
    - abseil/log/check (~> 1.20240116.2)
    - abseil/log/globals (~> 1.20240116.2)
    - abseil/log/log (~> 1.20240116.2)
    - abseil/memory/memory (~> 1.20240116.2)
    - abseil/meta/type_traits (~> 1.20240116.2)
    - abseil/random/bit_gen_ref (~> 1.20240116.2)
    - abseil/random/distributions (~> 1.20240116.2)
    - abseil/random/random (~> 1.20240116.2)
    - abseil/status/status (~> 1.20240116.2)
    - abseil/status/statusor (~> 1.20240116.2)
    - abseil/strings/cord (~> 1.20240116.2)
    - abseil/strings/str_format (~> 1.20240116.2)
    - abseil/strings/strings (~> 1.20240116.2)
    - abseil/synchronization/synchronization (~> 1.20240116.2)
    - abseil/time/time (~> 1.20240116.2)
    - abseil/types/optional (~> 1.20240116.2)
    - abseil/types/span (~> 1.20240116.2)
    - abseil/types/variant (~> 1.20240116.2)
    - abseil/utility/utility (~> 1.20240116.2)
    - BoringSSL-GRPC (= 0.0.36)
    - gRPC-Core/Interface (= 1.65.5)
    - gRPC-Core/Privacy (= 1.65.5)
  - gRPC-Core/Interface (1.65.5)
  - gRPC-Core/Privacy (1.65.5)
  - GTMAppAuth (4.1.1):
    - AppAuth/Core (~> 1.7)
    - GTMSessionFetcher/Core (< 4.0, >= 3.3)
  - GTMSessionFetcher (3.5.0):
    - GTMSessionFetcher/Full (= 3.5.0)
  - GTMSessionFetcher/Core (3.5.0)
  - GTMSessionFetcher/Full (3.5.0):
    - GTMSessionFetcher/Core
  - home_widget (0.0.1):
    - Flutter
  - image_editor_common (1.0.0):
    - Flutter
  - image_picker_ios (0.0.1):
    - Flutter
  - in_app_purchase_storekit (0.0.1):
    - Flutter
    - FlutterMacOS
  - in_app_review (2.0.0):
    - Flutter
  - integration_test (0.0.1):
    - Flutter
  - IosAwnCore (0.10.0)
  - just_audio (0.0.1):
    - Flutter
  - leveldb-library (1.22.6)
  - libwebp (1.5.0):
    - libwebp/demux (= 1.5.0)
    - libwebp/mux (= 1.5.0)
    - libwebp/sharpyuv (= 1.5.0)
    - libwebp/webp (= 1.5.0)
  - libwebp/demux (1.5.0):
    - libwebp/webp
  - libwebp/mux (1.5.0):
    - libwebp/demux
  - libwebp/sharpyuv (1.5.0)
  - libwebp/webp (1.5.0):
    - libwebp/sharpyuv
  - livekit_client (2.3.1):
    - Flutter
    - WebRTC-SDK (= 125.6422.06)
  - llama_library_flutter (0.0.1):
    - Flutter
  - local_auth_darwin (0.0.1):
    - Flutter
    - FlutterMacOS
  - location (0.0.1):
    - Flutter
  - maps_launcher (0.0.1):
    - Flutter
  - MediaPipeTasksGenAI (0.10.12):
    - MediaPipeTasksGenAIC (= 0.10.12)
  - MediaPipeTasksGenAIC (0.10.12)
  - nanopb (3.30910.0):
    - nanopb/decode (= 3.30910.0)
    - nanopb/encode (= 3.30910.0)
  - nanopb/decode (3.30910.0)
  - nanopb/encode (3.30910.0)
  - network_info_plus (0.0.1):
    - Flutter
  - onnxruntime (0.0.1):
    - Flutter
    - onnxruntime-objc (= 1.15.1)
  - onnxruntime-c (1.15.1)
  - onnxruntime-objc (1.15.1):
    - onnxruntime-objc/Core (= 1.15.1)
  - onnxruntime-objc/Core (1.15.1):
    - onnxruntime-c (= 1.15.1)
  - OrderedSet (6.0.3)
  - outetts_flutter (0.0.1):
    - Flutter
  - package_info_plus (0.4.5):
    - Flutter
  - path_provider_foundation (0.0.1):
    - Flutter
    - FlutterMacOS
  - pay_ios (0.0.1):
    - Flutter
  - pdfx (1.0.0):
    - Flutter
  - permission_handler_apple (9.3.0):
    - Flutter
  - platform_object_channel_foundation (0.0.1):
    - Flutter
    - FlutterMacOS
  - pointer_interceptor_ios (0.0.1):
    - Flutter
  - printing (1.0.0):
    - Flutter
  - PromisesObjC (2.4.0)
  - PromisesSwift (2.4.0):
    - PromisesObjC (= 2.4.0)
  - quick_actions_ios (0.0.1):
    - Flutter
  - quill_native_bridge (0.0.1):
    - Flutter
  - RecaptchaInterop (100.0.0)
  - record_darwin (1.0.0):
    - Flutter
  - SDWebImage (5.21.0):
    - SDWebImage/Core (= 5.21.0)
  - SDWebImage/Core (5.21.0)
  - sensors_plus (0.0.1):
    - Flutter
  - share_plus (0.0.1):
    - Flutter
  - shared_preferences_foundation (0.0.1):
    - Flutter
    - FlutterMacOS
  - sign_in_with_apple (0.0.1):
    - Flutter
  - speech_to_text (0.0.1):
    - Flutter
    - FlutterMacOS
    - Try
  - sqflite_darwin (0.0.4):
    - Flutter
    - FlutterMacOS
  - sqlite3 (3.47.2):
    - sqlite3/common (= 3.47.2)
  - sqlite3/common (3.47.2)
  - sqlite3/dbstatvtab (3.47.2):
    - sqlite3/common
  - sqlite3/fts5 (3.47.2):
    - sqlite3/common
  - sqlite3/perf-threadsafe (3.47.2):
    - sqlite3/common
  - sqlite3/rtree (3.47.2):
    - sqlite3/common
  - sqlite3_flutter_libs (0.0.1):
    - Flutter
    - FlutterMacOS
    - sqlite3 (~> 3.47.2)
    - sqlite3/dbstatvtab
    - sqlite3/fts5
    - sqlite3/perf-threadsafe
    - sqlite3/rtree
  - Stripe (23.30.0):
    - StripeApplePay (= 23.30.0)
    - StripeCore (= 23.30.0)
    - StripePayments (= 23.30.0)
    - StripePaymentsUI (= 23.30.0)
    - StripeUICore (= 23.30.0)
  - stripe_ios (0.0.1):
    - Flutter
    - Stripe (~> 23.30.0)
    - StripeApplePay (~> 23.30.0)
    - StripeFinancialConnections (~> 23.30.0)
    - StripePayments (~> 23.30.0)
    - StripePaymentSheet (~> 23.30.0)
    - StripePaymentsUI (~> 23.30.0)
  - StripeApplePay (23.30.0):
    - StripeCore (= 23.30.0)
  - StripeCore (23.30.0)
  - StripeFinancialConnections (23.30.0):
    - StripeCore (= 23.30.0)
    - StripeUICore (= 23.30.0)
  - StripePayments (23.30.0):
    - StripeCore (= 23.30.0)
    - StripePayments/Stripe3DS2 (= 23.30.0)
  - StripePayments/Stripe3DS2 (23.30.0):
    - StripeCore (= 23.30.0)
  - StripePaymentSheet (23.30.0):
    - StripeApplePay (= 23.30.0)
    - StripeCore (= 23.30.0)
    - StripePayments (= 23.30.0)
    - StripePaymentsUI (= 23.30.0)
  - StripePaymentsUI (23.30.0):
    - StripeCore (= 23.30.0)
    - StripePayments (= 23.30.0)
    - StripeUICore (= 23.30.0)
  - StripeUICore (23.30.0):
    - StripeCore (= 23.30.0)
  - super_keyboard (0.0.1):
    - Flutter
  - SwiftProtobuf (1.30.0)
  - SwiftyGif (5.4.5)
  - Try (2.1.1)
  - twitter_login (0.0.1):
    - Flutter
  - url_launcher_ios (0.0.1):
    - Flutter
  - video_player_avfoundation (0.0.1):
    - Flutter
    - FlutterMacOS
  - video_thumbnail (0.0.1):
    - Flutter
    - libwebp
  - wakelock_plus (0.0.1):
    - Flutter
  - WebRTC-SDK (125.6422.06)
  - webview_flutter_wkwebview (0.0.1):
    - Flutter
    - FlutterMacOS
  - whisper_library_flutter (0.0.1):
    - Flutter

DEPENDENCIES:
  - app_links (from `.symlinks/plugins/app_links/ios`)
  - audio_session (from `.symlinks/plugins/audio_session/ios`)
  - audioplayers_darwin (from `.symlinks/plugins/audioplayers_darwin/ios`)
  - awesome_notifications (from `.symlinks/plugins/awesome_notifications/ios`)
  - camera_avfoundation (from `.symlinks/plugins/camera_avfoundation/ios`)
  - cloud_firestore (from `.symlinks/plugins/cloud_firestore/ios`)
  - cloud_functions (from `.symlinks/plugins/cloud_functions/ios`)
  - connectivity_plus (from `.symlinks/plugins/connectivity_plus/darwin`)
  - desktop_webview_auth (from `.symlinks/plugins/desktop_webview_auth/ios`)
  - device_info_plus (from `.symlinks/plugins/device_info_plus/ios`)
  - file_picker (from `.symlinks/plugins/file_picker/ios`)
  - file_selector_ios (from `.symlinks/plugins/file_selector_ios/ios`)
  - firebase_analytics (from `.symlinks/plugins/firebase_analytics/ios`)
  - firebase_app_check (from `.symlinks/plugins/firebase_app_check/ios`)
  - firebase_auth (from `.symlinks/plugins/firebase_auth/ios`)
  - firebase_core (from `.symlinks/plugins/firebase_core/ios`)
  - firebase_crashlytics (from `.symlinks/plugins/firebase_crashlytics/ios`)
  - firebase_database (from `.symlinks/plugins/firebase_database/ios`)
  - firebase_dynamic_links (from `.symlinks/plugins/firebase_dynamic_links/ios`)
  - firebase_messaging (from `.symlinks/plugins/firebase_messaging/ios`)
  - firebase_ml_model_downloader (from `.symlinks/plugins/firebase_ml_model_downloader/ios`)
  - firebase_performance (from `.symlinks/plugins/firebase_performance/ios`)
  - firebase_remote_config (from `.symlinks/plugins/firebase_remote_config/ios`)
  - firebase_storage (from `.symlinks/plugins/firebase_storage/ios`)
  - FirebaseCoreExtension (= 11.6)
  - Flutter (from `Flutter`)
  - flutter_facebook_auth (from `.symlinks/plugins/flutter_facebook_auth/ios`)
  - flutter_file_dialog (from `.symlinks/plugins/flutter_file_dialog/ios`)
  - flutter_gemma (from `.symlinks/plugins/flutter_gemma/ios`)
  - flutter_icmp_ping (from `.symlinks/plugins/flutter_icmp_ping/ios`)
  - flutter_inappwebview_ios (from `.symlinks/plugins/flutter_inappwebview_ios/ios`)
  - flutter_keyboard_visibility (from `.symlinks/plugins/flutter_keyboard_visibility/ios`)
  - flutter_local_notifications (from `.symlinks/plugins/flutter_local_notifications/ios`)
  - flutter_native_splash (from `.symlinks/plugins/flutter_native_splash/ios`)
  - flutter_pdfview (from `.symlinks/plugins/flutter_pdfview/ios`)
  - flutter_stable_diffusion_core_ml (from `.symlinks/plugins/flutter_stable_diffusion_core_ml/darwin`)
  - flutter_tts (from `.symlinks/plugins/flutter_tts/ios`)
  - flutter_vision (from `.symlinks/plugins/flutter_vision/ios`)
  - flutter_webrtc (from `.symlinks/plugins/flutter_webrtc/ios`)
  - gal (from `.symlinks/plugins/gal/darwin`)
  - geolocator_apple (from `.symlinks/plugins/geolocator_apple/ios`)
  - ggml_library_flutter (from `.symlinks/plugins/ggml_library_flutter/ios`)
  - google_maps_flutter_ios (from `.symlinks/plugins/google_maps_flutter_ios/ios`)
  - google_sign_in_ios (from `.symlinks/plugins/google_sign_in_ios/darwin`)
  - home_widget (from `.symlinks/plugins/home_widget/ios`)
  - image_editor_common (from `.symlinks/plugins/image_editor_common/ios`)
  - image_picker_ios (from `.symlinks/plugins/image_picker_ios/ios`)
  - in_app_purchase_storekit (from `.symlinks/plugins/in_app_purchase_storekit/darwin`)
  - in_app_review (from `.symlinks/plugins/in_app_review/ios`)
  - integration_test (from `.symlinks/plugins/integration_test/ios`)
  - just_audio (from `.symlinks/plugins/just_audio/ios`)
  - livekit_client (from `.symlinks/plugins/livekit_client/ios`)
  - llama_library_flutter (from `.symlinks/plugins/llama_library_flutter/ios`)
  - local_auth_darwin (from `.symlinks/plugins/local_auth_darwin/darwin`)
  - location (from `.symlinks/plugins/location/ios`)
  - maps_launcher (from `.symlinks/plugins/maps_launcher/ios`)
  - network_info_plus (from `.symlinks/plugins/network_info_plus/ios`)
  - onnxruntime (from `.symlinks/plugins/onnxruntime/ios`)
  - outetts_flutter (from `.symlinks/plugins/outetts_flutter/ios`)
  - package_info_plus (from `.symlinks/plugins/package_info_plus/ios`)
  - path_provider_foundation (from `.symlinks/plugins/path_provider_foundation/darwin`)
  - pay_ios (from `.symlinks/plugins/pay_ios/ios`)
  - pdfx (from `.symlinks/plugins/pdfx/ios`)
  - permission_handler_apple (from `.symlinks/plugins/permission_handler_apple/ios`)
  - platform_object_channel_foundation (from `.symlinks/plugins/platform_object_channel_foundation/darwin`)
  - pointer_interceptor_ios (from `.symlinks/plugins/pointer_interceptor_ios/ios`)
  - printing (from `.symlinks/plugins/printing/ios`)
  - quick_actions_ios (from `.symlinks/plugins/quick_actions_ios/ios`)
  - quill_native_bridge (from `.symlinks/plugins/quill_native_bridge/ios`)
  - record_darwin (from `.symlinks/plugins/record_darwin/ios`)
  - sensors_plus (from `.symlinks/plugins/sensors_plus/ios`)
  - share_plus (from `.symlinks/plugins/share_plus/ios`)
  - shared_preferences_foundation (from `.symlinks/plugins/shared_preferences_foundation/darwin`)
  - sign_in_with_apple (from `.symlinks/plugins/sign_in_with_apple/ios`)
  - speech_to_text (from `.symlinks/plugins/speech_to_text/darwin`)
  - sqflite_darwin (from `.symlinks/plugins/sqflite_darwin/darwin`)
  - sqlite3_flutter_libs (from `.symlinks/plugins/sqlite3_flutter_libs/darwin`)
  - stripe_ios (from `.symlinks/plugins/stripe_ios/ios`)
  - super_keyboard (from `.symlinks/plugins/super_keyboard/ios`)
  - twitter_login (from `.symlinks/plugins/twitter_login/ios`)
  - url_launcher_ios (from `.symlinks/plugins/url_launcher_ios/ios`)
  - video_player_avfoundation (from `.symlinks/plugins/video_player_avfoundation/darwin`)
  - video_thumbnail (from `.symlinks/plugins/video_thumbnail/ios`)
  - wakelock_plus (from `.symlinks/plugins/wakelock_plus/ios`)
  - webview_flutter_wkwebview (from `.symlinks/plugins/webview_flutter_wkwebview/darwin`)
  - whisper_library_flutter (from `.symlinks/plugins/whisper_library_flutter/ios`)

SPEC REPOS:
  trunk:
    - abseil
    - AppAuth
    - AppCheckCore
    - BoringSSL-GRPC
    - DKImagePickerController
    - DKPhotoGallery
    - FBAEMKit
    - FBSDKCoreKit
    - FBSDKCoreKit_Basics
    - FBSDKLoginKit
    - Firebase
    - FirebaseABTesting
    - FirebaseAnalytics
    - FirebaseAppCheck
    - FirebaseAppCheckInterop
    - FirebaseAuth
    - FirebaseAuthInterop
    - FirebaseCore
    - FirebaseCoreExtension
    - FirebaseCoreInternal
    - FirebaseCrashlytics
    - FirebaseDatabase
    - FirebaseDynamicLinks
    - FirebaseFirestore
    - FirebaseFirestoreInternal
    - FirebaseFunctions
    - FirebaseInstallations
    - FirebaseMessaging
    - FirebaseMessagingInterop
    - FirebaseMLModelDownloader
    - FirebasePerformance
    - FirebaseRemoteConfig
    - FirebaseRemoteConfigInterop
    - FirebaseSessions
    - FirebaseSharedSwift
    - FirebaseStorage
    - Google-Maps-iOS-Utils
    - GoogleAppMeasurement
    - GoogleDataTransport
    - GoogleMaps
    - GoogleSignIn
    - GoogleUtilities
    - "gRPC-C++"
    - gRPC-Core
    - GTMAppAuth
    - GTMSessionFetcher
    - IosAwnCore
    - leveldb-library
    - libwebp
    - MediaPipeTasksGenAI
    - MediaPipeTasksGenAIC
    - nanopb
    - onnxruntime-c
    - onnxruntime-objc
    - OrderedSet
    - PromisesObjC
    - PromisesSwift
    - RecaptchaInterop
    - SDWebImage
    - sqlite3
    - Stripe
    - StripeApplePay
    - StripeCore
    - StripeFinancialConnections
    - StripePayments
    - StripePaymentSheet
    - StripePaymentsUI
    - StripeUICore
    - SwiftProtobuf
    - SwiftyGif
    - Try
    - WebRTC-SDK

EXTERNAL SOURCES:
  app_links:
    :path: ".symlinks/plugins/app_links/ios"
  audio_session:
    :path: ".symlinks/plugins/audio_session/ios"
  audioplayers_darwin:
    :path: ".symlinks/plugins/audioplayers_darwin/ios"
  awesome_notifications:
    :path: ".symlinks/plugins/awesome_notifications/ios"
  camera_avfoundation:
    :path: ".symlinks/plugins/camera_avfoundation/ios"
  cloud_firestore:
    :path: ".symlinks/plugins/cloud_firestore/ios"
  cloud_functions:
    :path: ".symlinks/plugins/cloud_functions/ios"
  connectivity_plus:
    :path: ".symlinks/plugins/connectivity_plus/darwin"
  desktop_webview_auth:
    :path: ".symlinks/plugins/desktop_webview_auth/ios"
  device_info_plus:
    :path: ".symlinks/plugins/device_info_plus/ios"
  file_picker:
    :path: ".symlinks/plugins/file_picker/ios"
  file_selector_ios:
    :path: ".symlinks/plugins/file_selector_ios/ios"
  firebase_analytics:
    :path: ".symlinks/plugins/firebase_analytics/ios"
  firebase_app_check:
    :path: ".symlinks/plugins/firebase_app_check/ios"
  firebase_auth:
    :path: ".symlinks/plugins/firebase_auth/ios"
  firebase_core:
    :path: ".symlinks/plugins/firebase_core/ios"
  firebase_crashlytics:
    :path: ".symlinks/plugins/firebase_crashlytics/ios"
  firebase_database:
    :path: ".symlinks/plugins/firebase_database/ios"
  firebase_dynamic_links:
    :path: ".symlinks/plugins/firebase_dynamic_links/ios"
  firebase_messaging:
    :path: ".symlinks/plugins/firebase_messaging/ios"
  firebase_ml_model_downloader:
    :path: ".symlinks/plugins/firebase_ml_model_downloader/ios"
  firebase_performance:
    :path: ".symlinks/plugins/firebase_performance/ios"
  firebase_remote_config:
    :path: ".symlinks/plugins/firebase_remote_config/ios"
  firebase_storage:
    :path: ".symlinks/plugins/firebase_storage/ios"
  Flutter:
    :path: Flutter
  flutter_facebook_auth:
    :path: ".symlinks/plugins/flutter_facebook_auth/ios"
  flutter_file_dialog:
    :path: ".symlinks/plugins/flutter_file_dialog/ios"
  flutter_gemma:
    :path: ".symlinks/plugins/flutter_gemma/ios"
  flutter_icmp_ping:
    :path: ".symlinks/plugins/flutter_icmp_ping/ios"
  flutter_inappwebview_ios:
    :path: ".symlinks/plugins/flutter_inappwebview_ios/ios"
  flutter_keyboard_visibility:
    :path: ".symlinks/plugins/flutter_keyboard_visibility/ios"
  flutter_local_notifications:
    :path: ".symlinks/plugins/flutter_local_notifications/ios"
  flutter_native_splash:
    :path: ".symlinks/plugins/flutter_native_splash/ios"
  flutter_pdfview:
    :path: ".symlinks/plugins/flutter_pdfview/ios"
  flutter_stable_diffusion_core_ml:
    :path: ".symlinks/plugins/flutter_stable_diffusion_core_ml/darwin"
  flutter_tts:
    :path: ".symlinks/plugins/flutter_tts/ios"
  flutter_vision:
    :path: ".symlinks/plugins/flutter_vision/ios"
  flutter_webrtc:
    :path: ".symlinks/plugins/flutter_webrtc/ios"
  gal:
    :path: ".symlinks/plugins/gal/darwin"
  geolocator_apple:
    :path: ".symlinks/plugins/geolocator_apple/ios"
  ggml_library_flutter:
    :path: ".symlinks/plugins/ggml_library_flutter/ios"
  google_maps_flutter_ios:
    :path: ".symlinks/plugins/google_maps_flutter_ios/ios"
  google_sign_in_ios:
    :path: ".symlinks/plugins/google_sign_in_ios/darwin"
  home_widget:
    :path: ".symlinks/plugins/home_widget/ios"
  image_editor_common:
    :path: ".symlinks/plugins/image_editor_common/ios"
  image_picker_ios:
    :path: ".symlinks/plugins/image_picker_ios/ios"
  in_app_purchase_storekit:
    :path: ".symlinks/plugins/in_app_purchase_storekit/darwin"
  in_app_review:
    :path: ".symlinks/plugins/in_app_review/ios"
  integration_test:
    :path: ".symlinks/plugins/integration_test/ios"
  just_audio:
    :path: ".symlinks/plugins/just_audio/ios"
  livekit_client:
    :path: ".symlinks/plugins/livekit_client/ios"
  llama_library_flutter:
    :path: ".symlinks/plugins/llama_library_flutter/ios"
  local_auth_darwin:
    :path: ".symlinks/plugins/local_auth_darwin/darwin"
  location:
    :path: ".symlinks/plugins/location/ios"
  maps_launcher:
    :path: ".symlinks/plugins/maps_launcher/ios"
  network_info_plus:
    :path: ".symlinks/plugins/network_info_plus/ios"
  onnxruntime:
    :path: ".symlinks/plugins/onnxruntime/ios"
  outetts_flutter:
    :path: ".symlinks/plugins/outetts_flutter/ios"
  package_info_plus:
    :path: ".symlinks/plugins/package_info_plus/ios"
  path_provider_foundation:
    :path: ".symlinks/plugins/path_provider_foundation/darwin"
  pay_ios:
    :path: ".symlinks/plugins/pay_ios/ios"
  pdfx:
    :path: ".symlinks/plugins/pdfx/ios"
  permission_handler_apple:
    :path: ".symlinks/plugins/permission_handler_apple/ios"
  platform_object_channel_foundation:
    :path: ".symlinks/plugins/platform_object_channel_foundation/darwin"
  pointer_interceptor_ios:
    :path: ".symlinks/plugins/pointer_interceptor_ios/ios"
  printing:
    :path: ".symlinks/plugins/printing/ios"
  quick_actions_ios:
    :path: ".symlinks/plugins/quick_actions_ios/ios"
  quill_native_bridge:
    :path: ".symlinks/plugins/quill_native_bridge/ios"
  record_darwin:
    :path: ".symlinks/plugins/record_darwin/ios"
  sensors_plus:
    :path: ".symlinks/plugins/sensors_plus/ios"
  share_plus:
    :path: ".symlinks/plugins/share_plus/ios"
  shared_preferences_foundation:
    :path: ".symlinks/plugins/shared_preferences_foundation/darwin"
  sign_in_with_apple:
    :path: ".symlinks/plugins/sign_in_with_apple/ios"
  speech_to_text:
    :path: ".symlinks/plugins/speech_to_text/darwin"
  sqflite_darwin:
    :path: ".symlinks/plugins/sqflite_darwin/darwin"
  sqlite3_flutter_libs:
    :path: ".symlinks/plugins/sqlite3_flutter_libs/darwin"
  stripe_ios:
    :path: ".symlinks/plugins/stripe_ios/ios"
  super_keyboard:
    :path: ".symlinks/plugins/super_keyboard/ios"
  twitter_login:
    :path: ".symlinks/plugins/twitter_login/ios"
  url_launcher_ios:
    :path: ".symlinks/plugins/url_launcher_ios/ios"
  video_player_avfoundation:
    :path: ".symlinks/plugins/video_player_avfoundation/darwin"
  video_thumbnail:
    :path: ".symlinks/plugins/video_thumbnail/ios"
  wakelock_plus:
    :path: ".symlinks/plugins/wakelock_plus/ios"
  webview_flutter_wkwebview:
    :path: ".symlinks/plugins/webview_flutter_wkwebview/darwin"
  whisper_library_flutter:
    :path: ".symlinks/plugins/whisper_library_flutter/ios"

SPEC CHECKSUMS:
  abseil: d121da9ef7e2ff4cab7666e76c5a3e0915ae08c3
  app_links: 3da4c36b46cac3bf24eb897f1a6ce80bda109874
  AppAuth: d4f13a8fe0baf391b2108511793e4b479691fb73
  AppCheckCore: cc8fd0a3a230ddd401f326489c99990b013f0c4f
  audio_session: f08db0697111ac84ba46191b55488c0563bb29c6
  audioplayers_darwin: ccf9c770ee768abb07e26d90af093f7bab1c12ab
  awesome_notifications: 0f432b28098d193920b11a44cfa9d2d9313a3888
  BoringSSL-GRPC: ca6a8e5d04812fce8ffd6437810c2d46f925eaeb
  camera_avfoundation: 04b44aeb14070126c6529e5ab82cc7c9fca107cf
  cloud_firestore: 7d187495c25a89cbcdb549dbd3402faf85b73702
  cloud_functions: ec25fb6ab468bf9485a2bdd0895f662aaa0f1bb7
  connectivity_plus: 2256d3e20624a7749ed21653aafe291a46446fee
  desktop_webview_auth: 4cf620136c3d79796496eef85e59508e3a1a003d
  device_info_plus: 71ffc6ab7634ade6267c7a93088ed7e4f74e5896
  DKImagePickerController: 946cec48c7873164274ecc4624d19e3da4c1ef3c
  DKPhotoGallery: b3834fecb755ee09a593d7c9e389d8b5d6deed60
  FBAEMKit: 6c7b5eb77c96861bb59e040842c6e55bf39512ce
  FBSDKCoreKit: 5e4dd478947ab1bcc887e8cfadeae0727af1a942
  FBSDKCoreKit_Basics: cd7b5f5d1e8868c26706917919d058999ca672c3
  FBSDKLoginKit: 572cca0bc6c90067ef197187697cb3b584310c52
  file_picker: 9b3292d7c8bc68c8a7bf8eb78f730e49c8efc517
  file_selector_ios: f92e583d43608aebc2e4a18daac30b8902845502
  Firebase: 374a441a91ead896215703a674d58cdb3e9d772b
  firebase_analytics: 879759d72b331897c77c1241b09f96a0103f6fe4
  firebase_app_check: 90eb4529b913788337a7c68c5ff0b3b5eb490273
  firebase_auth: ecd44c57a46c4942b58f13615e0bbb178ccff47a
  firebase_core: 2337982fb78ee4d8d91e608b0a3d4f44346a93c8
  firebase_crashlytics: 3b6a9a9cbdc5ab92afaf9b206e52c79c2321a0d4
  firebase_database: 6ee2def8969ae974f6bfcffcc18b5015d02f842e
  firebase_dynamic_links: 4979f43b85fdbe580099563fe47a0265ae4e622d
  firebase_messaging: 043fed3f1ffabab2483cab86263bdb56243f8323
  firebase_ml_model_downloader: 5b473fc9f0c3e0534f8ec68f749d14a32667214c
  firebase_performance: e8f0362a2ee84ed00d243b4404aba1a8284351d4
  firebase_remote_config: 8f6a6f30bb1ba9ea8ad341f73dd55f540d9b15f1
  firebase_storage: 78f9dda8df2e4abdd5bde6ff2f007c1ed7052b8c
  FirebaseABTesting: 663ece168d2d65a31f71603d71937e326020a887
  FirebaseAnalytics: 7114c698cac995602e3b1b96663473e50d54d6e7
  FirebaseAppCheck: 44803909e253d677481e9e16eb719b0f58a7ddc2
  FirebaseAppCheckInterop: 72066489c209823649a997132bcd9269bc33a4bb
  FirebaseAuth: 0304982cfe00df8d49bf533bc4becd3de36c7122
  FirebaseAuthInterop: 4fa327ec3c551a80a6929561f83af80b1dd44937
  FirebaseCore: 48b0dd707581cf9c1a1220da68223fb0a562afaa
  FirebaseCoreExtension: 2d77d6430c16cf43ca2b04608302ed02b3598361
  FirebaseCoreInternal: d98ab91e2d80a56d7b246856a8885443b302c0c2
  FirebaseCrashlytics: b21c665fb50138766480bce73ebdb1aa30f7f300
  FirebaseDatabase: ce3a83a39ab50559a85c5add54f6f285544433b8
  FirebaseDynamicLinks: bc4e79f608ce4ee066125695f20f2d9fc137d29e
  FirebaseFirestore: d5dcc15724f291fe4b415322754bfa01616037fe
  FirebaseFirestoreInternal: db478fdaeb98fe8686ff49e600f3871c224a76ff
  FirebaseFunctions: 6c4f1d143d3dc6ca1b5d9f4e042408ede2eb1016
  FirebaseInstallations: efc0946fc756e4d22d8113f7c761948120322e8c
  FirebaseMessaging: e1aca1fcc23e8b9eddb0e33f375ff90944623021
  FirebaseMessagingInterop: ad5d6bacc76f222424334f3e21bc21868eb9e23a
  FirebaseMLModelDownloader: 973a537765e127de4ccad8d5d825397506b9300c
  FirebasePerformance: eede3a4db492e4cc0e094cdc71a83896e948fea3
  FirebaseRemoteConfig: ee5161282c4e857ad81c0197cd8baec9d5dfef0e
  FirebaseRemoteConfigInterop: 7915cec47731a806cda541f90898ad0fab8f9f86
  FirebaseSessions: 9529d14180868e29a8da164b3a729c036204918b
  FirebaseSharedSwift: aca73668bc95e8efccb618e0167eab05d19d3a75
  FirebaseStorage: 52fb65a69d3e87675186881a08e2a5db86452ace
  Flutter: e0871f40cf51350855a761d2e70bf5af5b9b5de7
  flutter_facebook_auth: 81e624a905d30b1f97ebffb8b2feaf9e5330537a
  flutter_file_dialog: ca8d7fbd1772d4f0c2777b4ab20a7787ef4e7dd8
  flutter_gemma: e845dd437ead7e9764c3de8bd85d1ae9fe875249
  flutter_icmp_ping: 47c1df3440c18ddd39fc457e607bb3b42d4a339f
  flutter_inappwebview_ios: b89ba3482b96fb25e00c967aae065701b66e9b99
  flutter_keyboard_visibility: 4625131e43015dbbe759d9b20daaf77e0e3f6619
  flutter_local_notifications: 395056b3175ba4f08480a7c5de30cd36d69827e4
  flutter_native_splash: 6cad9122ea0fad137d23137dd14b937f3e90b145
  flutter_pdfview: 32bf27bda6fd85b9dd2c09628a824df5081246cf
  flutter_stable_diffusion_core_ml: 3ed7049714b28b842c575ee596e3e6239fc1d83b
  flutter_tts: b88dbc8655d3dc961bc4a796e4e16a4cc1795833
  flutter_vision: a3ec7f2c56ea8c35ff9a6025d43c61db16096460
  flutter_webrtc: 57f32415b8744e806f9c2a96ccdb60c6a627ba33
  gal: baecd024ebfd13c441269ca7404792a7152fde89
  geolocator_apple: 1560c3c875af2a412242c7a923e15d0d401966ff
  ggml_library_flutter: 7e8b42e28af9097c946f2d43154b7d1bc5ae01db
  Google-Maps-iOS-Utils: 0a484b05ed21d88c9f9ebbacb007956edd508a96
  google_maps_flutter_ios: 0291eb2aa252298a769b04d075e4a9d747ff7264
  google_sign_in_ios: 0ab078e60da6dfe23cbc55c83502b52bba1aad63
  GoogleAppMeasurement: 6a9e6317b6a6d810ad03d4a66564ca6c4c5818a3
  GoogleDataTransport: aae35b7ea0c09004c3797d53c8c41f66f219d6a7
  GoogleMaps: 0608099d4870cac8754bdba9b6953db543432438
  GoogleSignIn: d4281ab6cf21542b1cfaff85c191f230b399d2db
  GoogleUtilities: 00c88b9a86066ef77f0da2fab05f65d7768ed8e1
  "gRPC-C++": 2fa52b3141e7789a28a737f251e0c45b4cb20a87
  gRPC-Core: a27c294d6149e1c39a7d173527119cfbc3375ce4
  GTMAppAuth: f69bd07d68cd3b766125f7e072c45d7340dea0de
  GTMSessionFetcher: 5aea5ba6bd522a239e236100971f10cb71b96ab6
  home_widget: f169fc41fd807b4d46ab6615dc44d62adbf9f64f
  image_editor_common: 3de87e7c4804f4ae24c8f8a998362b98c105cac1
  image_picker_ios: 7fe1ff8e34c1790d6fff70a32484959f563a928a
  in_app_purchase_storekit: e126ef1b89e4a9fdf07e28f005f82632b4609437
  in_app_review: 5596fe56fab799e8edb3561c03d053363ab13457
  integration_test: 4a889634ef21a45d28d50d622cf412dc6d9f586e
  IosAwnCore: 653786a911089012092ce831f2945cd339855a89
  just_audio: 6c031bb61297cf218b4462be616638e81c058e97
  leveldb-library: cc8b8f8e013647a295ad3f8cd2ddf49a6f19be19
  libwebp: 02b23773aedb6ff1fd38cec7a77b81414c6842a8
  livekit_client: f2828bf3f9dae951af44107765b032a53f26fe45
  llama_library_flutter: 38a2e239ca404980adfec97f4ef0961cc5144331
  local_auth_darwin: 553ce4f9b16d3fdfeafce9cf042e7c9f77c1c391
  location: 155caecf9da4f280ab5fe4a55f94ceccfab838f8
  maps_launcher: edf829809ba9e894d70e569bab11c16352dedb45
  MediaPipeTasksGenAI: efd6d1177fcb6455a5460466c99229da4afe35bb
  MediaPipeTasksGenAIC: ed29bc83f4f39276505abfe956f4ff25f9c6a93a
  nanopb: fad817b59e0457d11a5dfbde799381cd727c1275
  network_info_plus: b6c3b27559bf719cc53ef571a947b3ac4629754c
  onnxruntime: 2ab1957eb029c71d47f7bb35cc4efc9f0d297828
  onnxruntime-c: ebdcfd8650bcbd10121c125262f99dea681b92a3
  onnxruntime-objc: ae7acec7a3d03eaf072d340afed7a35635c1c2a6
  OrderedSet: e539b66b644ff081c73a262d24ad552a69be3a94
  outetts_flutter: 611ed335638ab8787a8092615718a90556db53a4
  package_info_plus: af8e2ca6888548050f16fa2f1938db7b5a5df499
  path_provider_foundation: 080d55be775b7414fd5a5ef3ac137b97b097e564
  pay_ios: 2c48d76615ded0cfaff66185669501b8b1dfc55e
  pdfx: 77f4dddc48361fbb01486fa2bdee4532cbb97ef3
  permission_handler_apple: 4ed2196e43d0651e8ff7ca3483a069d469701f2d
  platform_object_channel_foundation: d6c7872f30a8c69da3bf685ddf6414ccca9061b6
  pointer_interceptor_ios: ec847ef8b0915778bed2b2cef636f4d177fa8eed
  printing: 54ff03f28fe9ba3aa93358afb80a8595a071dd07
  PromisesObjC: f5707f49cb48b9636751c5b2e7d227e43fba9f47
  PromisesSwift: 9d77319bbe72ebf6d872900551f7eeba9bce2851
  quick_actions_ios: 4b07fb49d8d8f3518d7565fbb7a91014067a7d82
  quill_native_bridge: fd2819cf6da02fb6cbf9de37835f96e798e145eb
  RecaptchaInterop: 7d1a4a01a6b2cb1610a47ef3f85f0c411434cb21
  record_darwin: fb1f375f1d9603714f55b8708a903bbb91ffdb0a
  SDWebImage: f84b0feeb08d2d11e6a9b843cb06d75ebf5b8868
  sensors_plus: 6a11ed0c2e1d0bd0b20b4029d3bad27d96e0c65b
  share_plus: 50da8cb520a8f0f65671c6c6a99b3617ed10a58a
  shared_preferences_foundation: 9e1978ff2562383bd5676f64ec4e9aa8fa06a6f7
  sign_in_with_apple: c5dcc141574c8c54d5ac99dd2163c0c72ad22418
  speech_to_text: 9dc43a5df3cbc2813f8c7cc9bd0fbf94268ed7ac
  sqflite_darwin: 20b2a3a3b70e43edae938624ce550a3cbf66a3d0
  sqlite3: 7559e33dae4c78538df563795af3a86fc887ee71
  sqlite3_flutter_libs: 5235ce0546528db87927a3ef1baff8b7d5107f0e
  Stripe: 9757efc154de1d9615cbea4836d590bc4034d3a4
  stripe_ios: ac48e0488f95ac7ddea9475fd30f3d739e0bae52
  StripeApplePay: ca33933601302742623762157d587b79b942d073
  StripeCore: 2af250a2366ff2bbf64d4243c5f9bbf2a98b2aaf
  StripeFinancialConnections: 3ab1ef6182ec44e71c29e9a2100b663f9713ac20
  StripePayments: 658a16bd34d20c8185aa281866227b9e1743300e
  StripePaymentSheet: eac031f76d7fbb4f52df9b9c39be5be671ca4c07
  StripePaymentsUI: 7d7cffb2ecfc0d6b5ac3a4488c02893a5ff6cc77
  StripeUICore: bb102d453b1e1a10a37f810bc0a9aa0675fb17fd
  super_keyboard: 016de6ce9ab826f9a0b185608209d6a3b556d577
  SwiftProtobuf: 3697407f0d5b23bedeba9c2eaaf3ec6fdff69349
  SwiftyGif: 706c60cf65fa2bc5ee0313beece843c8eb8194d4
  Try: 5ef669ae832617b3cee58cb2c6f99fb767a4ff96
  twitter_login: ea7069c8a3f4d3d786fae20495352c5e99926f03
  url_launcher_ios: 694010445543906933d732453a59da0a173ae33d
  video_player_avfoundation: 2cef49524dd1f16c5300b9cd6efd9611ce03639b
  video_thumbnail: b637e0ad5f588ca9945f6e2c927f73a69a661140
  wakelock_plus: 04623e3f525556020ebd4034310f20fe7fda8b49
  WebRTC-SDK: 79942c006ea64f6fb48d7da8a4786dfc820bc1db
  webview_flutter_wkwebview: 44d4dee7d7056d5ad185d25b38404436d56c547c
  whisper_library_flutter: beaed76e3d612fc13645eb758126df6598d1e29b

PODFILE CHECKSUM: aef3789a85c97a1868a0a82c54a4b680fea9833c

COCOAPODS: 1.16.2
